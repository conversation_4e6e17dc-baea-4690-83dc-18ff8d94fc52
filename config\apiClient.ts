import Constants from "expo-constants";
import { create, type ApisauceInstance } from "apisauce";

// Singleton instance - ensures only one apisauce instance is created for the entire app
let apisauceInstance: ApisauceInstance | null = null;

function getApisauceInstance(): ApisauceInstance {
	if (!apisauceInstance) {
		// Constants.expoConfig works in Expo managed workflow and EAS builds
		// process.env works in Metro bundler during development
		const baseUrl =
			Constants.expoConfig?.extra?.BASE_API_URL ||
			process.env.BASE_API_URL ||
			"localhost:8000";

		const fullBaseUrl =
			baseUrl.startsWith("http://") || baseUrl.startsWith("https://")
				? baseUrl
				: `http://${baseUrl}`;

		apisauceInstance = create({
			baseURL: fullBaseUrl,
			headers: {
				"Content-Type": "application/json",
				// Add any default headers here
			},
			timeout: 30000,
		});
	}
	return apisauceInstance;
}

export const api = getApisauceInstance();
