{"name": "touchat-ui", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "format": "biome format --write .", "lint": "biome lint .", "check": "biome check --write ."}, "dependencies": {"@better-auth/expo": "^1.3.4", "@expo-google-fonts/gabarito": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.2.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tamagui/config": "^1.132.16", "@tanstack/react-query": "^5.84.2", "apisauce": "^3.2.0", "better-auth": "^1.3.4", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "expo": "~53.0.22", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.7", "expo-network": "^7.1.5", "expo-router": "~5.1.5", "expo-secure-store": "^14.2.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.11", "expo-video-thumbnails": "^9.1.3", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tamagui": "^1.132.16", "zod": "^4.0.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@biomejs/biome": "^2.2.2", "@tamagui/babel-plugin": "^1.132.16", "@types/react": "~19.0.10", "eas-cli": "^16.17.4", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.8.3"}, "private": true, "packageManager": "bun@1.2.12"}