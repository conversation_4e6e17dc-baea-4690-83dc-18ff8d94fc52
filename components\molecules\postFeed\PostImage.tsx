import { Image, type ImageProps, YStack, Text, Spinner } from "tamagui";
import { useState } from "react";

export interface PostImageProps
	extends Omit<ImageProps, "source" | "onLoad" | "onError"> {
	src: string;
	alt?: string;
	aspectRatio?: number;
	showLoadingSpinner?: boolean;
	showErrorMessage?: boolean;
}

export const PostImage = ({
	src,
	alt,
	aspectRatio = 16 / 9,
	showLoadingSpinner = true,
	showErrorMessage = true,
	width = "100%",
	...props
}: PostImageProps) => {
	const [isLoading, setIsLoading] = useState(true);
	const [hasError, setHasError] = useState(false);

	const handleLoad = () => {
		setIsLoading(false);
		setHasError(false);
	};

	const handleError = () => {
		setIsLoading(false);
		setHasError(true);
	};

	if (!src) {
		return null;
	}

	if (hasError && showErrorMessage) {
		return (
			<YStack
				width={width}
				aspectRatio={aspectRatio}
				backgroundColor="$black"
				alignItems="center"
				justifyContent="center"
				{...props}
			>
				<Text color="$gray7">Failed to load image</Text>
			</YStack>
		);
	}

	return (
		<YStack position="relative" width={width} {...props}>
			{isLoading && showLoadingSpinner ? (
				<YStack
					position="absolute"
					top={0}
					left={0}
					right={0}
					bottom={0}
					alignItems="center"
					justifyContent="center"
					backgroundColor="$black"
					zIndex={1}
				>
					<Spinner size="large" color="white" />
				</YStack>
			) : null}
			<Image
				source={{ uri: src }}
				width={width}
				aspectRatio={aspectRatio}
				onLoad={handleLoad}
				onError={handleError}
				accessibilityLabel={alt}
				{...props}
			/>
		</YStack>
	);
};
