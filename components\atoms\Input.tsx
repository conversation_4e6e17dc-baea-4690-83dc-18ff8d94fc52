import React from "react";
import { TouchableOpacity } from "react-native";
import {
	Input as TamaguiInput,
	type InputProps as TamaguiInputProps,
	YStack,
	XStack,
	Text,
} from "tamagui";

export interface InputProps extends TamaguiInputProps {
	placeholder?: string;
	value?: string;
	onChangeText?: (text: string) => void;
	hasError?: boolean;
	errorStyle?: TamaguiInputProps;
	errorMessage?: string;
	label?: string;
	labelColor?: string;
	labelFontSize?: number;
	flex?: number | "unset";
	leftIcon?: React.ReactNode;
	rightIcon?: React.ReactNode;
	iconColor?: string;
	iconSize?: number;
	onRightIconPress?: () => void;
	rightIconPressable?: boolean;
}

export const Input = ({
	placeholder = "Enter text",
	placeholderTextColor = "$gray3",
	backgroundColor = "$gray1",
	borderColor = "$border-gray",
	borderRadius = 8,
	borderWidth = 1,
	height = 48,
	fontSize = 16,
	color = "white",
	hasError = false,
	errorStyle,
	errorMessage,
	value,
	onChangeText,
	label,
	labelColor = "white",
	labelFontSize = 14,
	flex,
	leftIcon,
	rightIcon,
	iconColor = "white",
	iconSize = 20,
	onRightIconPress,
	rightIconPressable = false,
	...props
}: InputProps) => {
	const finalStyle = hasError && errorStyle ? { ...errorStyle } : {};
	const finalBorderColor = hasError ? "$error" : borderColor;

	return (
		<YStack gap={4} flex={flex}>
			{label ? (
				<Text
					color={labelColor}
					fontSize={labelFontSize}
					fontWeight="400"
					marginBottom={4}
				>
					{label}
				</Text>
			) : null}
			<XStack position="relative" alignItems="center">
				{leftIcon ? (
					<XStack
						position="absolute"
						left={12}
						zIndex={1}
						alignItems="center"
						justifyContent="center"
						pointerEvents="none"
					>
						{React.isValidElement(leftIcon)
							? React.cloneElement(
									leftIcon as React.ReactElement,
									{
										size: iconSize,
										color: iconColor,
										...(leftIcon.props || {}),
									} as Partial<InputProps>,
								)
							: leftIcon}
					</XStack>
				) : null}
				<TamaguiInput
					placeholder={placeholder}
					placeholderTextColor={placeholderTextColor}
					backgroundColor={backgroundColor}
					borderColor={finalBorderColor}
					borderRadius={borderRadius}
					borderWidth={borderWidth}
					height={height}
					fontSize={fontSize}
					color={color}
					value={value}
					onChangeText={onChangeText}
					{...finalStyle}
					{...props}
				/>
				{rightIcon ? (
					<XStack
						position="absolute"
						right={12}
						zIndex={1}
						alignItems="center"
						justifyContent="center"
						pointerEvents={
							rightIconPressable || onRightIconPress ? "auto" : "none"
						}
					>
						{rightIconPressable || onRightIconPress ? (
							<TouchableOpacity
								onPress={onRightIconPress}
								style={{
									alignItems: "center",
									justifyContent: "center",
									padding: 4,
								}}
								activeOpacity={0.7}
							>
								{React.isValidElement(rightIcon)
									? React.cloneElement(
											rightIcon as React.ReactElement,
											{
												size: iconSize,
												color: iconColor,
												...(rightIcon.props || {}),
											} as Partial<InputProps>,
										)
									: rightIcon}
							</TouchableOpacity>
						) : React.isValidElement(rightIcon) ? (
							React.cloneElement(
								rightIcon as React.ReactElement,
								{
									size: iconSize,
									color: iconColor,
									...(rightIcon.props || {}),
								} as Partial<InputProps>,
							)
						) : (
							rightIcon
						)}
					</XStack>
				) : null}
			</XStack>
			{hasError && errorMessage ? (
				<Text
					color="$error"
					fontSize={12}
					fontWeight="400"
					marginTop={2}
					marginLeft={4}
				>
					{errorMessage}
				</Text>
			) : null}
		</YStack>
	);
};
