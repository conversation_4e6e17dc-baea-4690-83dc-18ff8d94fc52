# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug 
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Environment variables
.env
.env.*
!.env.example

# typescript
*.tsbuildinfo

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json 

# Tamagui
.tamagui/

vendor
