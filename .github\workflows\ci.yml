name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
    types: [opened, synchronize, reopened]

env:
  NODE_VERSION: "20"
  BUN_VERSION: "1.2.12"
  JAVA_VERSION: "17"
  RUBY_VERSION: "3.2"

jobs:
  # Lint and Type Check
  lint-and-typecheck:
    name: 🔍 Lint & Type Check
    runs-on: ubuntu-latest
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🔍 Run ESLint
        run: bun lint

      - name: 🔍 TypeScript type check
        run: bunx tsc --noEmit

  # Android Build
  android-build:
    name: 🤖 Android Build
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || (github.event_name == 'push' && github.ref == 'refs/heads/develop')
    needs: lint-and-typecheck
    concurrency: android-build-${{ github.run_id }}
    permissions:
      pull-requests: write
      actions: write
      contents: read
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ env.RUBY_VERSION }}
          bundler-cache: true

      - name: 🏗 Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: 🏗 Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: ${{ env.JAVA_VERSION }}

      - name: 🏗 Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: 🏗 Setup Android NDK
        uses: nttld/setup-ndk@v1
        with:
          ndk-version: r25c
          add-to-path: false

      - name: 📦 Install dependencies
        run: bun install --frozen-lockfile

      - name: 🏗 Setup AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_S3_REGION || 'eu-west-2' }}

      - name: 🏗 Setup EAS CLI
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          packager: bun

      - name: 🚀 Build Android APK with EAS Local
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            bundle exec fastlane android build_preview_eas_local
          else
            bundle exec fastlane android build_staging_eas_local
          fi
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          GITHUB_PR_TITLE: ${{ github.event.pull_request.title }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_S3_BUCKET_NAME: ${{ secrets.AWS_S3_BUCKET_NAME }}
          AWS_S3_REGION: ${{ secrets.AWS_S3_REGION }}
          ANDROID_KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
          ANDROID_STORE_PASSWORD: ${{ secrets.ANDROID_STORE_PASSWORD }}
          ANDROID_KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
          ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}

      - name: 📱 Upload APK artifact (backup to GitHub)
        uses: actions/upload-artifact@v4
        if: always() # Upload even if S3 upload fails
        with:
          name: android-apk-backup-${{ github.run_id }}
          path: |
            fastlane/build-*.apk
            build-*.tar.gz
            android/app/build/outputs/apk/release/*.apk
          retention-days: 7 # Shorter retention since S3 is primary storage
