import {
	Button as TamaguiButton,
	type ButtonProps as TamaguiButtonProps,
} from "tamagui";
import { Text } from "./Text";

export interface ButtonProps extends TamaguiButtonProps {
	isActive?: boolean;
	disabled?: boolean;
	title?: string;
	validBackgroundColor?: string;
	invalidBackgroundColor?: string;
	validTextColor?: string;
	invalidTextColor?: string;
}

export const Button = ({
	isActive = true,
	disabled = false,
	title,
	children,
	validBackgroundColor = "$primary",
	invalidBackgroundColor = "$gray3",
	validTextColor = "$black",
	invalidTextColor = "$gray3",
	borderRadius = 40,
	paddingVertical = 12,
	paddingHorizontal = 12,
	fontSize = 14,
	fontWeight = 600,
	height = "fill",
	onPress,
	width,
	...props
}: ButtonProps) => {
	const backgroundColor = isActive
		? validBackgroundColor
		: invalidBackgroundColor;
	const textColor = isActive ? validTextColor : invalidTextColor;

	return (
		<TamaguiButton
			backgroundColor={backgroundColor}
			height={height}
			borderRadius={borderRadius}
			paddingVertical={paddingVertical}
			paddingHorizontal={paddingHorizontal}
			disabled={disabled}
			pressStyle={{
				opacity: 0.6,
				backgroundColor: isActive
					? validBackgroundColor
					: invalidBackgroundColor,
				borderColor: "transparent",
			}}
			opacity={isActive ? 1 : 0.6}
			onPress={onPress}
			width={width}
			{...props}
		>
			<Text color={textColor} fontSize={fontSize} fontWeight={fontWeight}>
				{children || title}
			</Text>
		</TamaguiButton>
	);
};
