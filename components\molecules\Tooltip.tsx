import type React from "react";
import {
	XStack,
	type XStackProps,
	Text,
	YStack,
	type YStackProps,
} from "tamagui";
import { AnimatePresence, MotiView } from "moti";

export type TooltipPosition = "top" | "bottom" | "left" | "right";

export interface TooltipProps {
	children: React.ReactNode;
	position?: TooltipPosition;
	visible: boolean;
	pointerSize?: number;
	style?: XStackProps;
	containerStyle?: YStackProps;
}

interface PointerProps {
	position: TooltipPosition;
	backgroundColor: string;
	size: number;
}

const TooltipPointer = ({ position, backgroundColor, size }: PointerProps) => {
	const getPointerStyle = () => {
		const baseStyle = {
			position: "absolute" as const,
			width: 0,
			height: 0,
			borderLeftWidth: size,
			borderRightWidth: size,
			borderTopWidth: size,
			borderBottomWidth: size,
			borderLeftColor: "transparent",
			borderRightColor: "transparent",
			borderTopColor: "transparent",
			borderBottomColor: "transparent",
		};

		switch (position) {
			case "bottom":
				return {
					...baseStyle,
					top: "100%",
					left: "50%",
					marginLeft: -size,
					borderTopColor: backgroundColor,
					borderBottomWidth: 0,
				};
			case "top":
				return {
					...baseStyle,
					bottom: "100%",
					left: "50%",
					marginLeft: -size,
					borderBottomColor: backgroundColor,
					borderTopWidth: 0,
				};
			case "left":
				return {
					...baseStyle,
					right: "100%",
					top: "50%",
					marginTop: -size,
					borderRightColor: backgroundColor,
					borderLeftWidth: 0,
				};
			case "right":
				return {
					...baseStyle,
					left: "100%",
					top: "50%",
					marginTop: -size,
					borderLeftColor: backgroundColor,
					borderRightWidth: 0,
				};
			default:
				return baseStyle;
		}
	};

	return <XStack {...getPointerStyle()} />;
};

export const Tooltip = ({
	children,
	position = "bottom",
	visible,
	pointerSize = 6,
	style,
	containerStyle,
}: TooltipProps) => {
	return (
		<AnimatePresence>
			{visible && (
				<MotiView
					from={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					transition={{
						type: "timing",
						duration: 400,
					}}
					style={{ position: "absolute", zIndex: 1000 }}
				>
					<YStack {...containerStyle}>
						{/* Tooltip content */}
						<XStack
							backgroundColor={"white"}
							paddingHorizontal={8}
							paddingVertical={4}
							borderRadius={4}
							{...style}
						>
							{typeof children === "string" ? (
								<Text color={"$black"}>{children}</Text>
							) : (
								children
							)}
						</XStack>

						{/* Triangular pointer */}
						<TooltipPointer
							position={position}
							backgroundColor={"white"}
							size={pointerSize}
						/>
					</YStack>
				</MotiView>
			)}
		</AnimatePresence>
	);
};
