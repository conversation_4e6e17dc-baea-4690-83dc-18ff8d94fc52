import { View, Text, Button } from "tamagui";
import { Entypo } from "@expo/vector-icons";
import { UserAvatar } from "./UserAvatar";

export const UserProfileAvatar = ({
	imageUrl,
	initials,
	size = 60,
	onPress,
}: {
	imageUrl?: string;
	initials?: string;
	size?: number;
	onPress?: () => void;
}) => {
	return (
		<View width={size} height={size}>
			{imageUrl ? (
				<UserAvatar src={imageUrl} alt="User profile pic" size={size} />
			) : (
				<Text color="white" fontSize="$6">
					{initials}
				</Text>
			)}

			{/* Camera button */}
			<Button
				unstyled
				position="absolute"
				bottom={-5}
				right={-5}
				borderRadius={50}
				padding="$2"
				backgroundColor="white"
				onPress={onPress}
			>
				<Entypo name="camera" size={14} color="black" />
			</Button>
		</View>
	);
};
