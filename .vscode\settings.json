{
  // Set Biome as the default formatter for JavaScript and TypeScript
  "editor.defaultFormatter": "biomejs.biome",

  // Enable format on save
  "editor.formatOnSave": true,

  // Enable format on paste
  "editor.formatOnPaste": true,

  // Enable format on type
  "editor.formatOnType": true,

  // Set tab size to 2 spaces
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,

  // Language-specific settings
  "[javascript]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "quickfix.biome": "explicit",
      "source.organizeImports.biome": "explicit"
    }
  },

  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "quickfix.biome": "explicit",
      "source.organizeImports.biome": "explicit"
    }
  },

  "[json]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true
  },

  "[jsonc]": {
    "editor.defaultFormatter": "biomejs.biome",
    "editor.formatOnSave": true
  },

  // Disable other formatters to avoid conflicts
  "prettier.enable": false,
  "eslint.enable": false,

  // Biome-specific settings
  "biome.enabled": true,
  "biome.lsp.bin": "./node_modules/.bin/biome"
}
