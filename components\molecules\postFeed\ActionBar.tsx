import { XStack, type X<PERSON><PERSON>ck<PERSON>rops, Button } from "tamagui";
import { Bookmark, Comment, Repost, Like, Send, Views } from "@/assets/svg";
import { Tooltip } from "../Tooltip";
import { Text } from "../../atoms/Text";
import { useTooltip } from "@/hooks/useTooltip";
import { abbreviateNumber } from "@/utils/formatters";

export interface ActionBarProps extends XStackProps {
	viewCount: number;
	id: string;
}

const isLoggedIn = false;

export const ActionBar = ({ viewCount, id, ...props }: ActionBarProps) => {
	const { showTooltip, triggerTooltip } = useTooltip();

	const handleAction = (actionType: string) => {
		if (isLoggedIn) {
			return console.log(actionType, id);
		}

		triggerTooltip();
	};

	const actions = [
		{
			key: "bookmark",
			icon: <Bookmark />,
			onPress: () => handleAction("bookmark"),
		},
		{
			key: "comment",
			icon: <Comment />,
			onPress: () => handleAction("comment"),
		},
		{
			key: "repost",
			icon: <Repost />,
			onPress: () => handleAction("repost"),
		},
		{
			key: "like",
			icon: <Like />,
			onPress: () => handleAction("like"),
		},
		{
			key: "send",
			icon: <Send />,
			onPress: () => handleAction("send"),
		},
	];

	return (
		<XStack
			position="relative"
			height={52}
			alignItems="center"
			gap={16}
			pl={4}
			{...props}
		>
			<Tooltip
				visible={showTooltip}
				position="bottom"
				containerStyle={{
					position: "absolute",
					top: -50,
				}}
			>
				Login to interact with posts
			</Tooltip>
			{actions.map((action) => (
				<Button
					key={action.key}
					onPress={action.onPress}
					backgroundColor="transparent"
					borderWidth={0}
					padding={0}
					pressStyle={{ opacity: 0.6, backgroundColor: "transparent" }}
				>
					{action.icon}
				</Button>
			))}
			<XStack alignItems="center" gap={4} ml="auto">
				<Views />
				<Text>{abbreviateNumber(viewCount)}</Text>
			</XStack>
		</XStack>
	);
};
