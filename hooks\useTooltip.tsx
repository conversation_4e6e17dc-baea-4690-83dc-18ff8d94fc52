import { useState, useEffect, useRef, useCallback } from "react";

export interface UseTooltipOptions {
	/** Duration in milliseconds to show the tooltip before auto-hiding */
	duration?: number;
}

export interface UseTooltipReturn {
	showTooltip: boolean;
	triggerTooltip: () => void;
	hideTooltip: () => void;
	showTooltipManually: () => void;
}

/**
 * Custom hook for managing tooltip state with automatic timeout and cleanup.
 */
export const useTooltip = ({
	duration = 1500,
}: UseTooltipOptions = {}): UseTooltipReturn => {
	const [showTooltip, setShowTooltip] = useState(false);
	const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

	const clearExistingTimeout = useCallback(() => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
	}, []);

	const hideTooltip = useCallback(() => {
		clearExistingTimeout();
		setShowTooltip(false);
	}, [clearExistingTimeout]);

	const showTooltipManually = useCallback(() => {
		clearExistingTimeout();
		setShowTooltip(true);

		timeoutRef.current = setTimeout(() => {
			setShowTooltip(false);
		}, duration);
	}, [duration, clearExistingTimeout]);

	const triggerTooltip = useCallback(() => {
		showTooltipManually();
	}, [showTooltipManually]);

	// Cleanup timeout on unmount
	useEffect(() => {
		return () => {
			clearExistingTimeout();
		};
	}, [clearExistingTimeout]);

	return {
		showTooltip,
		triggerTooltip,
		hideTooltip,
		showTooltipManually,
	};
};
