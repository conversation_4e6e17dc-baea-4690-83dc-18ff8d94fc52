const { getDefaultConfig } = require("expo/metro-config");

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

// <PERSON>agu<PERSON> requires these extensions to be added to Metro
config.resolver.sourceExts.push("mjs");

// Add SVG support - treat SVG as source files, not assets
config.resolver.sourceExts.push("svg");
config.resolver.assetExts = config.resolver.assetExts.filter(
  (ext) => ext !== "svg"
);

// Configure SVG transformer
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve("react-native-svg-transformer"),
};

// Add support for .web.js files for better web compatibility
config.resolver.platforms = ["ios", "android", "native", "web"];

module.exports = config;
