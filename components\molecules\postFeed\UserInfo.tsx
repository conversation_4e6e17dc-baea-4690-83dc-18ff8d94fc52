import { XStack, YStack, type XStackProps, Text as TamaguiText } from "tamagui";
import { UserAvatar, Text } from "../../atoms";
import { formatRelativeTime } from "../../../utils/formatters";

export interface UserInfoProps extends XStackProps {
	avatar: string;
	name: string;
	handle: string;
	timestamp: number;
	isReddit: boolean;
}

export const UserInfo = ({
	avatar,
	name,
	handle,
	timestamp,
	isReddit,
	...props
}: UserInfoProps) => {
	return (
		<XStack mb={16} {...props}>
			<UserAvatar src={avatar} alt={name} mr={12} />
			<YStack>
				<XStack flex={1} alignItems="center">
					<TamaguiText fontSize={16} fontWeight={700} color="white">
						{name}
					</TamaguiText>
					<Text> • {formatRelativeTime(timestamp)}</Text>
				</XStack>
				{!isReddit ? (
					<Text color="$gray7" textDecorationLine="underline">
						{handle}
					</Text>
				) : null}
			</YStack>
		</XStack>
	);
};
