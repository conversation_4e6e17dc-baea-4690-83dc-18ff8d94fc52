# fastlane/Fastfile

require 'base64'
require 'aws-sdk-s3'

default_platform(:android)

# Helper method to get app version from package.json
def get_app_version
  package_json = JSON.parse(File.read("../package.json"))
  package_json["version"]
end

# Helper method to get build number (timestamp-based)
def get_build_number
  Time.now.strftime("%Y%m%d%H%M")
end

# Helper method to clean build directories
def clean_build_directories
  UI.message("🧹 Cleaning build directories...")
  sh("rm -rf ../android/app/build") if Dir.exist?("../android/app/build")
end

# Helper method to send Slack notification
def send_slack_notification(message:, success: true, build_info: nil)
  webhook_url = ENV['SLACK_WEBHOOK_URL']
  return unless webhook_url

  color = success ? "good" : "danger"
  emoji = success ? "✅" : "❌"

  # Build the message with optional build info
  full_message = "#{emoji} #{message}"
  if build_info
    full_message += "\n📱 App Version: #{build_info[:app_version]}"
    full_message += "\n🔢 Build Number: #{build_info[:build_number]}"
    full_message += "\n📦 Platform: #{build_info[:platform]}"
    if build_info[:s3_download_url]
      full_message += "\n🔗 Download APK: #{build_info[:s3_download_url]}"
    elsif build_info[:artifact_path]
      full_message += "\n📁 Local Artifact: #{File.basename(build_info[:artifact_path])}"
    end
    if build_info[:git_commit_hash]
      full_message += "\n🔗 Commit: #{build_info[:git_commit_hash][0..7]}"
    end
  end

  payload = {
    attachments: [{
      color: color,
      text: full_message,
      footer: "Fastlane Local Build",
      ts: Time.now.to_i
    }]
  }

  begin
    require 'net/http'
    require 'json'
    uri = URI(webhook_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    request = Net::HTTP::Post.new(uri)
    request['Content-Type'] = 'application/json'
    request.body = payload.to_json
    response = http.request(request)

    if response.code == '200'
      UI.success("✅ Slack notification sent successfully")
    else
      UI.error("❌ Failed to send Slack notification: #{response.code}")
    end
  rescue => ex
    UI.error("❌ Error sending Slack notification: #{ex.message}")
  end
end

# Helper method to create build info for local builds
def create_local_build_info(platform:, profile:, artifact_path: nil, s3_url: nil)
  {
    platform: platform,
    profile: profile,
    app_version: get_app_version,
    build_number: get_build_number,
    git_commit_hash: ENV['GITHUB_SHA'] || `git rev-parse HEAD`.strip,
    artifact_path: artifact_path,
    s3_download_url: s3_url,
    created_at: Time.now.iso8601
  }
end

# Helper method to generate pre-signed URL for S3 object
def generate_presigned_url(bucket_name:, s3_key:, expiration: 604800) # 7 days default
  begin
    s3_client = Aws::S3::Client.new(
      access_key_id: ENV['AWS_ACCESS_KEY_ID'],
      secret_access_key: ENV['AWS_SECRET_ACCESS_KEY'],
      region: ENV['AWS_S3_REGION'] || 'eu-west-2'
    )

    presigner = Aws::S3::Presigner.new(client: s3_client)
    presigned_url = presigner.presigned_url(
      :get_object,
      bucket: bucket_name,
      key: s3_key,
      expires_in: expiration
    )

    return presigned_url
  rescue => ex
    UI.error("❌ Failed to generate pre-signed URL: #{ex.message}")
    return nil
  end
end

# Helper method to upload APK to S3
def upload_apk_to_s3(apk_path:, platform:, profile:)
  begin
    # Validate required environment variables
    required_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_S3_BUCKET_NAME']
    missing_vars = required_vars.select { |var| ENV[var].nil? || ENV[var].empty? }

    if missing_vars.any?
      UI.error("❌ Missing AWS environment variables: #{missing_vars.join(', ')}")
      UI.error("💡 S3 upload will be skipped. APK is still available locally.")
      return nil
    end

    # Generate S3 key with organized structure
    timestamp = Time.now.strftime("%Y%m%d_%H%M%S")
    commit_hash = ENV['GITHUB_SHA'] || `git rev-parse HEAD`.strip[0..7]
    apk_filename = File.basename(apk_path)
    s3_key = "builds/#{platform}/#{profile}/#{timestamp}_#{commit_hash}/#{apk_filename}"

    UI.message("📤 Uploading APK to S3...")
    UI.message("   Bucket: #{ENV['AWS_S3_BUCKET_NAME']}")
    UI.message("   Key: #{s3_key}")

    # Upload to S3 using AWS CLI (more reliable than plugin)
    sh("aws configure set aws_access_key_id #{ENV['AWS_ACCESS_KEY_ID']}")
    sh("aws configure set aws_secret_access_key #{ENV['AWS_SECRET_ACCESS_KEY']}")
    sh("aws configure set default.region #{ENV['AWS_S3_REGION'] || 'eu-west-2'}")

    # Upload file to S3
    sh("aws s3 cp '#{apk_path}' 's3://#{ENV['AWS_S3_BUCKET_NAME']}/#{s3_key}'")

    # Generate pre-signed download URL (valid for 7 days)
    s3_url = generate_presigned_url(
      bucket_name: ENV['AWS_S3_BUCKET_NAME'],
      s3_key: s3_key,
      expiration: 604800 # 7 days in seconds
    )

    UI.success("✅ APK uploaded to S3 successfully!")
    UI.message("🔗 Download URL: #{s3_url}")

    return s3_url

  rescue => ex
    UI.error("❌ S3 upload failed: #{ex.message}")
    UI.error("💡 Build will continue without S3 upload. APK is still available locally.")
    return nil
  end
end

# Helper method to setup keystore credentials for EAS local builds
def setup_keystore_credentials_for_eas
  timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
  UI.message("🔑 DEBUG [#{timestamp}] Setting up keystore credentials for EAS local build...")

  # Check if all required keystore environment variables are present
  required_vars = ['ANDROID_KEYSTORE_BASE64', 'ANDROID_STORE_PASSWORD', 'ANDROID_KEY_ALIAS', 'ANDROID_KEY_PASSWORD']
  missing_vars = required_vars.select { |var| ENV[var].nil? || ENV[var].empty? }

  if missing_vars.any?
    UI.error("❌ ERROR [#{timestamp}] EAS local build requires keystore credentials")
    UI.error("❌ ERROR [#{timestamp}] Missing variables: #{missing_vars.join(', ')}")
    return false
  end

  # Validate base64 keystore format
  begin
    keystore_data = Base64.strict_decode64(ENV['ANDROID_KEYSTORE_BASE64'])
    UI.message("🔑 DEBUG [#{timestamp}] ✅ Keystore base64 format is valid (#{keystore_data.length} bytes)")
  rescue => ex
    UI.error("❌ ERROR [#{timestamp}] Invalid base64 format for ANDROID_KEYSTORE_BASE64: #{ex.message}")
    return false
  end

  # Create keystore file from base64
  keystore_path = File.join(Dir.pwd, "../android-keystore.jks")
  UI.message("🔑 DEBUG [#{timestamp}] Creating keystore file at: #{keystore_path}")

  begin
    File.write(keystore_path, keystore_data, mode: 'wb')
    created_size = File.size(keystore_path)
    UI.message("🔑 DEBUG [#{timestamp}] ✅ Keystore file created successfully (#{created_size} bytes)")

    # Validate keystore file by attempting to list its contents
    UI.message("🔑 DEBUG [#{timestamp}] Validating keystore file integrity...")
    begin
      # Try to list keystore contents to verify it's valid
      keytool_output = sh("keytool -list -keystore '#{keystore_path}' -storepass '#{ENV['ANDROID_STORE_PASSWORD']}' -v", log: false)
      UI.message("🔑 DEBUG [#{timestamp}] ✅ Keystore validation successful")

      # Check if the specified alias exists
      if keytool_output.include?(ENV['ANDROID_KEY_ALIAS'])
        UI.message("🔑 DEBUG [#{timestamp}] ✅ Key alias '#{ENV['ANDROID_KEY_ALIAS']}' found in keystore")

        # Test key password by attempting to access the private key
        UI.message("🔑 DEBUG [#{timestamp}] Validating key password...")
        begin
          sh("keytool -list -keystore '#{keystore_path}' -storepass '#{ENV['ANDROID_STORE_PASSWORD']}' -alias '#{ENV['ANDROID_KEY_ALIAS']}' -keypass '#{ENV['ANDROID_KEY_PASSWORD']}'", log: false)
          UI.message("🔑 DEBUG [#{timestamp}] ✅ Key password validation successful")
        rescue => key_ex
          UI.error("❌ ERROR [#{timestamp}] Key password validation failed: #{key_ex.message}")
          UI.error("❌ ERROR [#{timestamp}] The ANDROID_KEY_PASSWORD is likely incorrect")
          return false
        end

      else
        UI.error("❌ ERROR [#{timestamp}] Key alias '#{ENV['ANDROID_KEY_ALIAS']}' NOT found in keystore")
        UI.error("❌ ERROR [#{timestamp}] Available aliases in keystore:")
        keytool_output.scan(/Alias name: (.+)/).each do |match|
          UI.error("❌ ERROR [#{timestamp}]   - #{match[0]}")
        end
        return false
      end

    rescue => keytool_ex
      UI.error("❌ ERROR [#{timestamp}] Keystore validation failed: #{keytool_ex.message}")
      UI.error("❌ ERROR [#{timestamp}] This usually means:")
      UI.error("❌ ERROR [#{timestamp}]   1. Keystore password is incorrect")
      UI.error("❌ ERROR [#{timestamp}]   2. Keystore file is corrupted")
      UI.error("❌ ERROR [#{timestamp}]   3. Base64 encoding/decoding issue")
      return false
    end

  rescue => ex
    UI.error("❌ ERROR [#{timestamp}] Failed to create keystore file: #{ex.message}")
    return false
  end

  # Set EAS environment variables for keystore
  UI.message("🔑 DEBUG [#{timestamp}] Setting EAS keystore environment variables...")

  # EAS expects these specific environment variable names
  ENV['EXPO_ANDROID_KEYSTORE_PATH'] = keystore_path
  ENV['EXPO_ANDROID_KEYSTORE_PASSWORD'] = ENV['ANDROID_STORE_PASSWORD']
  ENV['EXPO_ANDROID_KEY_ALIAS'] = ENV['ANDROID_KEY_ALIAS']
  ENV['EXPO_ANDROID_KEY_PASSWORD'] = ENV['ANDROID_KEY_PASSWORD']

  UI.message("🔑 DEBUG [#{timestamp}] ✅ EAS keystore environment variables set:")
  UI.message("🔑 DEBUG [#{timestamp}]   EXPO_ANDROID_KEYSTORE_PATH: #{keystore_path}")
  UI.message("🔑 DEBUG [#{timestamp}]   EXPO_ANDROID_KEYSTORE_PASSWORD: [SET]")
  UI.message("🔑 DEBUG [#{timestamp}]   EXPO_ANDROID_KEY_ALIAS: #{ENV['ANDROID_KEY_ALIAS']}")
  UI.message("🔑 DEBUG [#{timestamp}]   EXPO_ANDROID_KEY_PASSWORD: [SET]")

  # Log credential status (without exposing sensitive data)
  UI.message("🔑 DEBUG [#{timestamp}] Keystore credentials setup:")
  UI.message("🔑 DEBUG [#{timestamp}]   ANDROID_KEYSTORE_BASE64: ✅ Valid (#{ENV['ANDROID_KEYSTORE_BASE64'].length} chars)")
  UI.message("🔑 DEBUG [#{timestamp}]   ANDROID_STORE_PASSWORD: ✅ Present (#{ENV['ANDROID_STORE_PASSWORD'].length} chars)")
  UI.message("🔑 DEBUG [#{timestamp}]   ANDROID_KEY_ALIAS: ✅ Present (#{ENV['ANDROID_KEY_ALIAS']})")
  UI.message("🔑 DEBUG [#{timestamp}]   ANDROID_KEY_PASSWORD: ✅ Present (#{ENV['ANDROID_KEY_PASSWORD'].length} chars)")
  UI.message("🔑 DEBUG [#{timestamp}]   Keystore file: #{keystore_path}")
  UI.message("🔑 DEBUG [#{timestamp}]   Credentials file: #{credentials_path}")

  UI.message("🔑 DEBUG [#{timestamp}] ✅ All keystore credentials setup completed successfully")
  return true
end

# Helper method to cleanup keystore files after build
def cleanup_keystore_files
  timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
  UI.message("🔑 DEBUG [#{timestamp}] Cleaning up temporary keystore files...")

  keystore_path = File.join(Dir.pwd, "../android-keystore.jks")

  begin
    if File.exist?(keystore_path)
      File.delete(keystore_path)
      UI.message("🔑 DEBUG [#{timestamp}] ✅ Deleted keystore file: #{keystore_path}")
    end

    # Clear EAS keystore environment variables
    ['EXPO_ANDROID_KEYSTORE_PATH', 'EXPO_ANDROID_KEYSTORE_PASSWORD',
     'EXPO_ANDROID_KEY_ALIAS', 'EXPO_ANDROID_KEY_PASSWORD'].each do |var|
      ENV.delete(var)
    end
    UI.message("🔑 DEBUG [#{timestamp}] ✅ Cleared EAS keystore environment variables")

    UI.message("🔑 DEBUG [#{timestamp}] ✅ Keystore cleanup completed")
  rescue => ex
    UI.error("❌ ERROR [#{timestamp}] Failed to cleanup keystore files: #{ex.message}")
  end
end

# Helper method for comprehensive pre-build validation
def validate_build_environment
  timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
  UI.message("🔍 DEBUG [#{timestamp}] Starting pre-build environment validation...")

  # 1. Current working directory and file structure
  current_dir = Dir.pwd
  UI.message("🔍 DEBUG [#{timestamp}] Current working directory: #{current_dir}")

  # List key files and directories (relative to project root, not fastlane directory)
  key_paths = ['../package.json', '../eas.json', '../android', '../app.json', '../app.config.js', '../expo.json']
  key_paths.each do |path|
    full_path = File.join(current_dir, path)
    display_path = path.gsub('../', '')
    if File.exist?(full_path) || Dir.exist?(full_path)
      if File.directory?(full_path)
        UI.message("🔍 DEBUG [#{timestamp}] Directory exists: #{display_path}/")
      else
        file_size = File.size(full_path)
        UI.message("🔍 DEBUG [#{timestamp}] File exists: #{display_path} (#{file_size} bytes)")
      end
    else
      UI.message("⚠️ WARNING [#{timestamp}] Missing: #{display_path}")
    end
  end

  # 2. EAS CLI authentication status
  begin
    UI.message("🔍 DEBUG [#{timestamp}] Checking EAS CLI authentication...")
    whoami_output = sh("eas whoami", log: false)
    UI.message("🔍 DEBUG [#{timestamp}] EAS CLI authenticated as: #{whoami_output.strip}")
  rescue => ex
    UI.error("❌ ERROR [#{timestamp}] EAS CLI authentication failed: #{ex.message}")
    raise "EAS CLI not authenticated. Please run 'eas login' first."
  end

  # 3. Environment variables validation
  UI.message("🔍 DEBUG [#{timestamp}] Checking environment variables...")

  # Critical environment variables
  critical_vars = ['EXPO_TOKEN']
  critical_vars.each do |var|
    if ENV[var] && !ENV[var].empty?
      UI.message("🔍 DEBUG [#{timestamp}] ✅ #{var}: [PRESENT - #{ENV[var].length} chars]")
    else
      UI.error("❌ ERROR [#{timestamp}] Missing critical environment variable: #{var}")
    end
  end

  # Required keystore variables from GitHub Secrets
  UI.message("🔍 DEBUG [#{timestamp}] Validating Android keystore credentials from GitHub Secrets...")

  keystore_vars = ['ANDROID_KEYSTORE_BASE64', 'ANDROID_STORE_PASSWORD', 'ANDROID_KEY_ALIAS', 'ANDROID_KEY_PASSWORD']
  missing_keystore_vars = []

  keystore_vars.each do |var|
    if ENV[var] && !ENV[var].empty?
      UI.message("🔍 DEBUG [#{timestamp}] ✅ #{var}: [PRESENT - #{ENV[var].length} chars]")
    else
      UI.error("❌ ERROR [#{timestamp}] ❌ #{var}: [MISSING]")
      missing_keystore_vars << var
    end
  end

  if missing_keystore_vars.any?
    UI.error("❌ ERROR [#{timestamp}] Missing required Android keystore environment variables:")
    missing_keystore_vars.each { |var| UI.error("❌ ERROR [#{timestamp}]   - #{var}") }
    UI.error("❌ ERROR [#{timestamp}]")
    UI.error("❌ ERROR [#{timestamp}] These variables must be set from GitHub Secrets in your workflow:")
    UI.error("❌ ERROR [#{timestamp}]")
    UI.error("❌ ERROR [#{timestamp}] 1. Create/upload your Android keystore to GitHub Secrets:")
    UI.error("❌ ERROR [#{timestamp}]    - ANDROID_KEYSTORE_BASE64: base64-encoded keystore file")
    UI.error("❌ ERROR [#{timestamp}]    - ANDROID_STORE_PASSWORD: keystore password")
    UI.error("❌ ERROR [#{timestamp}]    - ANDROID_KEY_ALIAS: key alias name")
    UI.error("❌ ERROR [#{timestamp}]    - ANDROID_KEY_PASSWORD: key password")
    UI.error("❌ ERROR [#{timestamp}]")
    UI.error("❌ ERROR [#{timestamp}] 2. Add them to your GitHub Actions workflow:")
    UI.error("❌ ERROR [#{timestamp}]    env:")
    UI.error("❌ ERROR [#{timestamp}]      ANDROID_KEYSTORE_BASE64: \${{ secrets.ANDROID_KEYSTORE_BASE64 }}")
    UI.error("❌ ERROR [#{timestamp}]      ANDROID_STORE_PASSWORD: \${{ secrets.ANDROID_STORE_PASSWORD }}")
    UI.error("❌ ERROR [#{timestamp}]      ANDROID_KEY_ALIAS: \${{ secrets.ANDROID_KEY_ALIAS }}")
    UI.error("❌ ERROR [#{timestamp}]      ANDROID_KEY_PASSWORD: \${{ secrets.ANDROID_KEY_PASSWORD }}")
    UI.error("❌ ERROR [#{timestamp}]")
    UI.error("❌ ERROR [#{timestamp}] 3. To create a keystore file:")
    UI.error("❌ ERROR [#{timestamp}]    keytool -genkey -v -keystore android-keystore.jks \\")
    UI.error("❌ ERROR [#{timestamp}]            -alias your-key-alias -keyalg RSA -keysize 2048 \\")
    UI.error("❌ ERROR [#{timestamp}]            -validity 10000")
    UI.error("❌ ERROR [#{timestamp}]")
    UI.error("❌ ERROR [#{timestamp}] 4. Convert keystore to base64:")
    UI.error("❌ ERROR [#{timestamp}]    base64 -i android-keystore.jks | tr -d '\\n'")
    UI.error("❌ ERROR [#{timestamp}]")
    raise "Missing required Android keystore credentials from GitHub Secrets"
  else
    UI.message("🔍 DEBUG [#{timestamp}] ✅ All Android keystore credentials are present")
  end

  # 4. eas.json file validation (look in project root, not fastlane directory)
  eas_json_path = File.join(current_dir, '../eas.json')
  if File.exist?(eas_json_path)
    begin
      eas_config = JSON.parse(File.read(eas_json_path))
      UI.message("🔍 DEBUG [#{timestamp}] ✅ eas.json is valid JSON")

      # Check for preview profile
      if eas_config.dig('build', 'preview')
        UI.message("🔍 DEBUG [#{timestamp}] ✅ Preview profile found in eas.json")
        preview_config = eas_config['build']['preview']
        UI.message("🔍 DEBUG [#{timestamp}] Preview config: #{preview_config.to_json}")
      else
        UI.error("❌ ERROR [#{timestamp}] Preview profile not found in eas.json")
      end
    rescue JSON::ParserError => ex
      UI.error("❌ ERROR [#{timestamp}] eas.json is invalid JSON: #{ex.message}")
      UI.error("❌ ERROR [#{timestamp}] JSON error at position: #{ex.message.match(/position (\d+)/)[1] if ex.message.match(/position (\d+)/)}")

      # Show content around the error position if possible
      begin
        content = File.read(eas_json_path)
        UI.error("❌ ERROR [#{timestamp}] eas.json content (first 500 chars):")
        UI.error(content[0..499])
      rescue => read_ex
        UI.error("❌ ERROR [#{timestamp}] Could not read eas.json content: #{read_ex.message}")
      end
      raise "Invalid eas.json file"
    end
  else
    UI.error("❌ ERROR [#{timestamp}] eas.json file not found at: #{eas_json_path}")
    raise "eas.json file is required for EAS builds"
  end

  # 5. Android directory validation (if applicable) - look in project root
  android_dir = File.join(current_dir, '../android')
  if Dir.exist?(android_dir)
    UI.message("🔍 DEBUG [#{timestamp}] ✅ Android directory exists")

    # Check for key Android files
    android_files = ['../android/build.gradle', '../android/app/build.gradle', '../android/gradle.properties']
    android_files.each do |file|
      display_file = file.gsub('../', '')
      if File.exist?(File.join(current_dir, file))
        UI.message("🔍 DEBUG [#{timestamp}] ✅ #{display_file} exists")
      else
        UI.message("⚠️ WARNING [#{timestamp}] #{display_file} not found")
      end
    end
  else
    UI.message("🔍 DEBUG [#{timestamp}] ⚠️ Android directory not found (may be generated during build)")
  end

  # 6. System information
  begin
    # Disk space
    disk_info = sh("df -h .", log: false)
    UI.message("🔍 DEBUG [#{timestamp}] Disk space info:")
    disk_info.split("\n").each { |line| UI.message("🔍 DEBUG [#{timestamp}]   #{line}") }

    # Memory info (macOS)
    if RUBY_PLATFORM.include?('darwin')
      memory_info = sh("vm_stat", log: false)
      UI.message("🔍 DEBUG [#{timestamp}] Memory info (first 5 lines):")
      memory_info.split("\n")[0..4].each { |line| UI.message("🔍 DEBUG [#{timestamp}]   #{line}") }
    end
  rescue => ex
    UI.message("🔍 DEBUG [#{timestamp}] Could not get system info: #{ex.message}")
  end

  UI.message("🔍 DEBUG [#{timestamp}] Pre-build validation completed")
end

# Helper method for post-build failure analysis
def analyze_build_failure(error_message, exit_code = nil)
  timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
  UI.error("❌ ERROR [#{timestamp}] Starting post-build failure analysis...")

  UI.error("❌ ERROR [#{timestamp}] Build failure details:")
  UI.error("❌ ERROR [#{timestamp}]   Error message: #{error_message}")
  UI.error("❌ ERROR [#{timestamp}]   Exit code: #{exit_code}") if exit_code

  current_dir = Dir.pwd

  # 1. List all files in current directory and subdirectories
  UI.error("❌ ERROR [#{timestamp}] Current directory structure:")
  begin
    Dir.glob("**/*", File::FNM_DOTMATCH).sort.each do |file|
      next if file == '.' || file == '..'
      if File.directory?(file)
        UI.error("❌ ERROR [#{timestamp}]   DIR:  #{file}/")
      else
        size = File.size(file)
        UI.error("❌ ERROR [#{timestamp}]   FILE: #{file} (#{size} bytes)")
      end
    end
  rescue => ex
    UI.error("❌ ERROR [#{timestamp}] Could not list directory structure: #{ex.message}")
  end

  # 2. Look for EAS-specific error files and build logs
  eas_log_patterns = [
    "**/*eas*.log",
    "**/*build*.log",
    "**/*error*.log",
    "**/build-*.tar.gz",
    "**/eas-build-*.json"
  ]

  UI.error("❌ ERROR [#{timestamp}] Searching for EAS build artifacts and logs...")
  eas_log_patterns.each do |pattern|
    Dir.glob(pattern).each do |log_file|
      UI.error("❌ ERROR [#{timestamp}] Found potential log file: #{log_file}")
      if File.size(log_file) < 10000  # Only show small log files
        begin
          content = File.read(log_file)
          UI.error("❌ ERROR [#{timestamp}] Content of #{log_file}:")
          content.split("\n").first(20).each do |line|
            UI.error("❌ ERROR [#{timestamp}]   #{line}")
          end
        rescue => ex
          UI.error("❌ ERROR [#{timestamp}] Could not read #{log_file}: #{ex.message}")
        end
      else
        UI.error("❌ ERROR [#{timestamp}] Log file #{log_file} is too large (#{File.size(log_file)} bytes)")
      end
    end
  end

  # 3. Check for partial build artifacts
  build_artifacts = Dir.glob("build-*")
  if build_artifacts.any?
    UI.error("❌ ERROR [#{timestamp}] Found partial build artifacts:")
    build_artifacts.each do |artifact|
      if File.directory?(artifact)
        UI.error("❌ ERROR [#{timestamp}]   DIR: #{artifact}/")
      else
        UI.error("❌ ERROR [#{timestamp}]   FILE: #{artifact} (#{File.size(artifact)} bytes)")
      end
    end
  else
    UI.error("❌ ERROR [#{timestamp}] No build artifacts found")
  end

  # 4. System information at time of failure
  begin
    UI.error("❌ ERROR [#{timestamp}] System information at failure:")

    # Available disk space
    disk_info = sh("df -h .", log: false)
    UI.error("❌ ERROR [#{timestamp}] Disk space:")
    disk_info.split("\n").each { |line| UI.error("❌ ERROR [#{timestamp}]   #{line}") }

    # Process information
    if RUBY_PLATFORM.include?('darwin')
      top_info = sh("top -l 1 -n 5", log: false)
      UI.error("❌ ERROR [#{timestamp}] Top processes:")
      top_info.split("\n")[0..10].each { |line| UI.error("❌ ERROR [#{timestamp}]   #{line}") }
    end
  rescue => ex
    UI.error("❌ ERROR [#{timestamp}] Could not get system information: #{ex.message}")
  end

  UI.error("❌ ERROR [#{timestamp}] Post-build failure analysis completed")
end


platform :android do
  desc "Build preview APK for pull requests using EAS local build"
  lane :build_preview_eas_local do
    UI.message("🚀 Building preview APK using EAS local build for pull request...")

    begin
      # Comprehensive pre-build validation
      validate_build_environment

      # Setup keystore credentials for EAS local build
      unless setup_keystore_credentials_for_eas
        raise "Keystore credentials setup failed"
      end

      # Clean build directories
      clean_build_directories

      # Build Android APK using EAS local build with enhanced error handling
      UI.message("🔧 Running EAS build --local for preview profile...")

      timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
      UI.message("🔍 DEBUG [#{timestamp}] Executing EAS build command...")
      UI.message("🔍 DEBUG [#{timestamp}] Command: eas build --platform android --profile preview --local --non-interactive")

      begin
        # Capture both stdout and stderr
        build_output = sh("eas build --platform android --profile preview --local --non-interactive 2>&1", log: true)
        UI.message("🔍 DEBUG [#{timestamp}] EAS build command completed successfully")

        # Log the build output for debugging
        UI.message("🔍 DEBUG [#{timestamp}] Build output (last 50 lines):")
        build_output.split("\n").last(50).each_with_index do |line, index|
          UI.message("🔍 DEBUG [#{timestamp}] [#{index + 1}] #{line}")
        end

      rescue => build_ex
        timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
        UI.error("❌ ERROR [#{timestamp}] EAS build command failed!")
        UI.error("❌ ERROR [#{timestamp}] Exception class: #{build_ex.class}")
        UI.error("❌ ERROR [#{timestamp}] Exception message: #{build_ex.message}")

        # Try to get exit code if available
        exit_code = build_ex.respond_to?(:exit_code) ? build_ex.exit_code : 'unknown'
        UI.error("❌ ERROR [#{timestamp}] Exit code: #{exit_code}")

        # Capture any available output
        if build_ex.respond_to?(:output) && build_ex.output
          UI.error("❌ ERROR [#{timestamp}] Command output:")
          build_ex.output.split("\n").each_with_index do |line, index|
            UI.error("❌ ERROR [#{timestamp}] [#{index + 1}] #{line}")
          end
        end

        # Perform detailed failure analysis
        analyze_build_failure(build_ex.message, exit_code)

        raise build_ex
      end

      # Find the generated APK (EAS local builds create APK directly)
      UI.message("🔍 Looking for EAS build artifacts...")

      # EAS local builds can create APK files directly or in tar.gz archives
      # First, look for direct APK files
      apk_files = Dir.glob("build-*.apk")

      # If no direct APK files, look for tar.gz archives
      if apk_files.empty?
        UI.message("📦 No direct APK found, looking for build archives...")
        build_archives = Dir.glob("../build-*.tar.gz")

        if build_archives.any?
          build_file = build_archives.first
          UI.message("📦 Extracting build archive: #{build_file}")
          sh("tar -xzf #{build_file}")
          apk_files = Dir.glob("**/*.apk")
        end
      end

      if apk_files.any?
        UI.success("✅ Preview APK build completed!")
        apk_path = apk_files.first

        # Upload APK to S3
        s3_url = upload_apk_to_s3(
          apk_path: apk_path,
          platform: "android",
          profile: "preview"
        )

        # Create build info with S3 URL
        build_info = create_local_build_info(
          platform: "android",
          profile: "preview",
          artifact_path: apk_path,
          s3_url: s3_url
        )

        # Send Slack notification with S3 download link
        pr_title = ENV['GITHUB_PR_TITLE'] || "Pull Request"
        message = "Android Preview APK built successfully with EAS local build for: #{pr_title}"
        if s3_url
          message += "\n🔗 Download APK: #{s3_url}"
        end

        send_slack_notification(
          message: message,
          success: true,
          build_info: build_info
        )

        UI.message("📁 Local APK location: #{apk_path}")
        UI.message("🔗 S3 download URL: #{s3_url}") if s3_url

        # Clean up temporary keystore files
        cleanup_keystore_files
      else
        error_msg = "APK file not found after EAS build"
        UI.error("❌ ERROR APK file not found after EAS build")
        analyze_build_failure(error_msg)

        # Clean up temporary keystore files even on failure
        cleanup_keystore_files
        raise error_msg
      end

    rescue => ex
      timestamp = Time.now.strftime("%Y-%m-%d %H:%M:%S")
      UI.error("❌ ERROR [#{timestamp}] Preview APK build failed: #{ex.message}")
      UI.error("❌ ERROR [#{timestamp}] Exception backtrace:")
      ex.backtrace.first(10).each { |line| UI.error("❌ ERROR [#{timestamp}]   #{line}") }

      # Clean up temporary keystore files on exception
      cleanup_keystore_files

      send_slack_notification(
        message: "Android Preview APK build failed: #{ex.message}",
        success: false
      )
      raise ex
    end
  end

  desc "Build staging APK using EAS local build"
  lane :build_staging_eas_local do
    UI.message("🚀 Building staging APK using EAS local build...")

    begin
      # Clean build directories
      clean_build_directories

      # Build Android APK using EAS local build
      UI.message("� Running EAS build --local for preview profile...")
      sh("eas build --platform android --profile preview --local --non-interactive")

      # Find the generated APK (EAS local builds create APK directly)
      UI.message("🔍 Looking for EAS build artifacts...")

      # EAS local builds can create APK files directly or in tar.gz archives
      # First, look for direct APK files
      apk_files = Dir.glob("build-*.apk")

      # If no direct APK files, look for tar.gz archives
      if apk_files.empty?
        UI.message("📦 No direct APK found, looking for build archives...")
        build_archives = Dir.glob("../build-*.tar.gz")

        if build_archives.any?
          build_file = build_archives.first
          UI.message("📦 Extracting build archive: #{build_file}")
          sh("tar -xzf #{build_file}")
          apk_files = Dir.glob("**/*.apk")
        end
      end

      if apk_files.any?
        UI.success("✅ Staging APK build completed!")
        apk_path = apk_files.first

        # Upload APK to S3
        s3_url = upload_apk_to_s3(
          apk_path: apk_path,
          platform: "android",
          profile: "staging"
        )

        # Create build info with S3 URL
        build_info = create_local_build_info(
          platform: "android",
          profile: "staging",
          artifact_path: apk_path,
          s3_url: s3_url
        )

        # Send Slack notification with S3 download link
        message = "Android Staging APK built successfully with EAS local build"
        if s3_url
          message += "\n🔗 Download APK: #{s3_url}"
        end

        send_slack_notification(
          message: message,
          success: true,
          build_info: build_info
        )

        UI.message("📁 Local APK location: #{apk_path}")
        UI.message("🔗 S3 download URL: #{s3_url}") if s3_url
      else
        raise "APK file not found after EAS build"
      end

    rescue => ex
      UI.error("❌ Staging APK build failed: #{ex.message}")
      send_slack_notification(
        message: "Android Staging APK build failed: #{ex.message}",
        success: false
      )
      raise ex
    end
  end
end




