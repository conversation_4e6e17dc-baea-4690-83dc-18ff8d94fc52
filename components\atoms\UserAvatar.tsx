import { Avatar, type AvatarProps } from "tamagui";

export interface UserAvatarProps extends Omit<AvatarProps, "children"> {
	src?: string;
	alt?: string;
	size?: number;
}

export const UserAvatar = ({
	src,
	alt = "User Avatar",
	size = 40,
	...props
}: UserAvatarProps) => {
	return (
		<Avatar circular {...props} size={size}>
			<Avatar.Image accessibilityLabel={alt} src={src} />
			<Avatar.Fallback backgroundColor="$gray8" />
		</Avatar>
	);
};
