import { XStack, Image, type XStackProps } from "tamagui";
import Reactions from "@/assets/dummy/reactions.png";
import { Text } from "../../atoms/Text";

export interface EngagementMetricsProps extends XStackProps {
	likeCount: number;
	commentCount: number;
	repostCount: number;
}

export const EngagementMetrics = ({
	likeCount,
	commentCount,
	repostCount,
	alignItems = "center",
	mt = 16,
	...props
}: EngagementMetricsProps) => {
	const formatCount = (count: number): string => {
		return count.toLocaleString();
	};

	return (
		<XStack alignItems={alignItems} mt={mt} {...props}>
			<Image source={Reactions} height={28} width={60} />
			<Text ml={10}>{formatCount(likeCount)}</Text>
			<Text color="$gray7" marginLeft="auto">
				{formatCount(commentCount)} comments • {formatCount(repostCount)}{" "}
				reposts
			</Text>
		</XStack>
	);
};
