import { YStack, Image } from "tamagui";
import { Text, SlideToStart } from "@/components";
import { LogoCF } from "@/assets/svg";
import AuthBackground from "@/assets/images/auth-bg.png";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useUserStatus } from "@/hooks/useUserStatus";
import { TAB_ROUTES } from "@/constants/routes";

export default function LoginOrSignUpScreen() {
	const router = useRouter();
	const insets = useSafeAreaInsets();
	const { markUserAsReturning } = useUserStatus();

	const handleSlideSuccess = async () => {
		await markUserAsReturning();
		router.replace(TAB_ROUTES.HOME);
	};

	return (
		<YStack flex={1} position="relative" backgroundColor="$bg-dark">
			<Image // Background Image
				source={AuthBackground}
				position="absolute"
				top={0}
				left={0}
				right={0}
				bottom={0}
				objectFit="cover"
				zIndex={0}
				height={"100%"}
				width={"100%"}
			/>
			<YStack flex={1} paddingHorizontal={32} pt={20 + insets.top}>
				<LogoCF />
				<Text fontSize={32} mt={"auto"}>
					All crypto conversations.{" "}
				</Text>
				<Text fontSize={32} color={"$primary"} fontWeight={600}>
					One feed.
				</Text>
			</YStack>
			<SlideToStart onSlideSuccess={handleSlideSuccess} />
		</YStack>
	);
}
