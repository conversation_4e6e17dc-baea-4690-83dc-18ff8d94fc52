import { runOnJS } from "react-native-reanimated";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { <PERSON>ack, XStack, YStack } from "tamagui";
import { <PERSON><PERSON>View, useDynamicAnimation } from "moti";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Text } from "../atoms";
import { useNavigationBarHeight } from "@/hooks";
import {
	MARGIN_HORIZONTAL,
	SLIDER_HEIGHT,
	SLIDER_PADDING,
	SLIDER_WIDTH,
	HANDLE_SIZE,
	SLIDE_RANGE,
} from "@/constants/slider";

type SlideToStartProps = {
	onSlideSuccess: () => void;
};

export function SlideToStart({ onSlideSuccess }: SlideToStartProps) {
	const { navigationBarHeight } = useNavigationBarHeight();

	const handleAnimation = useDynamicAnimation(() => ({
		translateX: 0,
	}));

	const textAnimation = useDynamicAnimation(() => ({
		opacity: 1,
	}));

	// Pan gesture for dragging the handle
	const panGesture = Gesture.Pan()
		.onUpdate((event) => {
			const newTranslateX = Math.max(
				0,
				Math.min(event.translationX, SLIDE_RANGE),
			);

			handleAnimation.animateTo({
				translateX: newTranslateX,
			});

			textAnimation.animateTo({
				opacity: 1 - newTranslateX / SLIDE_RANGE,
			});
		})
		.onEnd((event) => {
			const newTranslateX = Math.max(
				0,
				Math.min(event.translationX, SLIDE_RANGE),
			);

			const progress = (newTranslateX / SLIDE_RANGE) * 100;

			// If the slider is dragged past 90%, complete the slide
			if (progress > 90) {
				handleAnimation.animateTo({
					translateX: SLIDE_RANGE,
				});
				textAnimation.animateTo({
					opacity: 0,
				});
				// Run the JS-thread functions after the gesture ends
				runOnJS(onSlideSuccess)();
			} else {
				// Otherwise, spring back to the start position
				handleAnimation.animateTo({
					translateX: 0,
				});
				textAnimation.animateTo({
					opacity: 1,
				});
			}
		});

	return (
		<GestureDetector gesture={panGesture}>
			<Stack
				width={SLIDER_WIDTH}
				height={SLIDER_HEIGHT}
				padding={SLIDER_PADDING}
				marginHorizontal={MARGIN_HORIZONTAL}
				backgroundColor="$gray2"
				borderRadius={99}
				justifyContent="center"
				alignItems="center"
				mt={56}
				mb={50 + navigationBarHeight}
			>
				<MotiView state={textAnimation} transition={{ type: "spring" }}>
					<Text letterSpacing={1.5}>GET STARTED</Text>
				</MotiView>

				{/* Animated chevrons on the right */}
				<XStack position="absolute" right={12} alignItems="center">
					{[0, 1, 2].map((i) => (
						<MotiView
							key={i}
							from={{ opacity: 0.5 }}
							animate={{ opacity: 0 }}
							transition={{
								type: "timing",
								duration: 1000,
								delay: i * 300,
								loop: true,
							}}
							style={{
								marginLeft: -22,
							}}
						>
							<MaterialCommunityIcons
								name="chevron-right"
								size={30}
								color="white"
							/>
						</MotiView>
					))}
				</XStack>

				{/* Draggable Slider Handle */}
				<YStack position="absolute" left={4} justifyContent="center">
					<MotiView
						state={handleAnimation}
						transition={{ type: "spring", damping: 20, mass: 0.1 }}
						style={{
							width: HANDLE_SIZE,
							height: HANDLE_SIZE,
							borderRadius: 99,
							backgroundColor: "white",
							justifyContent: "center",
							alignItems: "center",
						}}
					>
						<MaterialCommunityIcons
							name="arrow-right"
							size={30}
							color="black"
						/>
					</MotiView>
				</YStack>
			</Stack>
		</GestureDetector>
	);
}
