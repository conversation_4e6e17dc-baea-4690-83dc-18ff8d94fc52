import { XStack, type XStackProps, Button, Text as TamaguiText } from "tamagui";
import {
	LogoSmallReddit,
	LogoSmallTelegram,
	LogoSmallTwitter,
	TripleChevronRight,
} from "@/assets/svg";
import { Text } from "../../atoms/Text";
import { capitalizeFirstLetter } from "@/utils/string";

export interface PostHeaderProps extends XStackProps {
	platformName: string;
	channelName: string;
	onNavigate?: () => void;
}

export const PostHeader = ({
	platformName,
	channelName,
	onNavigate,
	...props
}: PostHeaderProps) => {
	const isReddit = platformName === "reddit";
	const isTelegram = platformName === "telegram";

	const sourcePlatform = () => {
		const source = capitalizeFirstLetter(platformName);
		if (isReddit || isTelegram) {
			return `${source} Channel • `;
		}
		return "X Post • ";
	};

	const getPlatformIcon = () => {
		if (isReddit) {
			return <LogoSmallReddit />;
		}
		if (isTelegram) {
			return <LogoSmallTelegram />;
		}
		return <LogoSmallTwitter />;
	};

	const getChannelName = () => {
		if (isReddit || isTelegram) {
			return channelName;
		}
		return "Most recent";
	};

	return (
		<XStack flex={1} alignItems="center" {...props}>
			{getPlatformIcon()}
			<TamaguiText fontWeight={700} color="white" ml={10}>
				{sourcePlatform()}
			</TamaguiText>
			<Text textDecorationLine={isReddit || isTelegram ? "underline" : "none"}>
				{getChannelName()}
			</Text>
			<Button
				onPress={onNavigate}
				backgroundColor="transparent"
				borderWidth={0}
				padding={0}
				ml="auto"
				height={"fill"}
				pressStyle={{ opacity: 0.6, backgroundColor: "transparent" }}
			>
				<TripleChevronRight />
			</Button>
		</XStack>
	);
};
