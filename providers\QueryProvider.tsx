import type React from "react";
import { useState } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { createQueryClient } from "@/config";
import { useReactQueryNativeSetup } from "@/hooks";

interface QueryProviderProps {
	children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
	// Lazy initialization - function only runs once
	const [queryClient] = useState(() => createQueryClient());

	// Set up React Native-specific optimizations
	useReactQueryNativeSetup();

	return (
		<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
	);
}
