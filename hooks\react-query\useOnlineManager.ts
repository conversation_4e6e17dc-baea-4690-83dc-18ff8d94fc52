import { useEffect } from "react";
import { onlineManager } from "@tanstack/react-query";
import * as Network from "expo-network";

/**
 * Hook to manage React Query's online state using Expo Network
 * This enables automatic refetching when the device reconnects to the internet
 */
export function useOnlineManager() {
	useEffect(() => {
		// Set up network state listener using Expo Network
		const unsubscribe = onlineManager.setEventListener((setOnline) => {
			const eventSubscription = Network.addNetworkStateListener((state) => {
				setOnline(!!state.isConnected);
			});

			// Return cleanup function
			return eventSubscription.remove;
		});

		return unsubscribe;
	}, []);
}
