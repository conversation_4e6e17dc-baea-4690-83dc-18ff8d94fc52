/** biome-ignore-all lint/suspicious/noExplicitAny: type definitions require any for flexibility */
declare module "*.svg" {
	import type React from "react";
	import type { SvgProps } from "react-native-svg";
	const content: React.FC<SvgProps>;
	export default content;
}

declare module "*.svg?react" {
	import type React from "react";
	import type { SvgProps } from "react-native-svg";
	const content: React.FC<SvgProps>;
	export default content;
}

// Image file declarations
declare module "*.png" {
	const value: any;
	export default value;
}

declare module "*.jpg" {
	const value: any;
	export default value;
}
