require("dotenv").config();

export default {
  expo: {
    name: "TouChat",
    slug: "TouChat",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "touchatui",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    statusBar: {
      style: "light",
    },
    extra: {
      BASE_API_URL: process.env.BASE_API_URL || "localhost:8000",
      eas: {
        projectId: "15aa5b87-1038-4b23-8332-78798b824ccc",
      },
    },
    ios: {
      supportsTablet: true,
      statusBarStyle: "light",
      config: {
        usesNonExemptEncryption: false,
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#00151B",
      },
      edgeToEdgeEnabled: true,
      statusBar: {
        style: "light",
      },
      navigationBar: {
        style: "light",
        backgroundColor: "#00151B",
      },
      package: "com.touchat.app",
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/splash-icon.png",
          imageWidth: 200,
          resizeMode: "contain",
          backgroundColor: "#ffffff",
        },
      ],
      [
        "expo-secure-store",
        {
          configureAndroidBackup: true,
          faceIDPermission:
            "Allow $(PRODUCT_NAME) to access your Face ID biometric data.",
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
  },
};
