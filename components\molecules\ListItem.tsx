import { TouchableOpacity } from "react-native";
import { Image, XStack } from "tamagui";
import { Checkbox, Text } from "../atoms";

// types
import type React from "react";

export interface ListItemProps {
	name: string;
	symbol: string;
	imageUrl: string;
	onPress: () => void;
	checked?: boolean;
	disabled?: boolean;
	hideCheckbox?: boolean;
}

export const ListItem: React.FC<ListItemProps> = ({
	name,
	symbol,
	imageUrl,
	onPress,
	checked = false,
	disabled = false,
	hideCheckbox = false,
}) => {
	const handlePress = () => {
		if (!disabled && onPress) {
			onPress();
		}
	};
	return (
		<TouchableOpacity
			onPress={handlePress}
			disabled={disabled}
			activeOpacity={disabled ? 1 : 0.8}
		>
			<XStack
				flex={1}
				alignItems="center"
				justifyContent="space-between"
				py={16}
				opacity={disabled ? 0.5 : 1}
			>
				<XStack alignItems="center">
					<Image
						source={{
							uri: imageUrl,
						}}
						width={24}
						height={24}
						borderRadius={12}
						objectFit="cover"
					/>
					<Text
						color={checked ? "$primary" : "$white1"}
						fontSize={18}
						mx={12}
						lineHeight={18 * 1.3}
					>
						{name}
					</Text>
					<Text
						color={checked ? "$primary" : "$gray3"}
						fontSize={12}
						lineHeight={12 * 1.5}
					>
						{symbol}
					</Text>
				</XStack>
				{!hideCheckbox ? <Checkbox checked={checked} /> : null}
			</XStack>
		</TouchableOpacity>
	);
};
