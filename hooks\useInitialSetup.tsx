import { useEffect } from "react";
import { Platform } from "react-native";
import { useFonts } from "expo-font";
import { useFonts as useGabaritoFonts } from "@expo-google-fonts/gabarito/useFonts";
import {
	Gabarito_400Regular,
	Gabarito_500Medium,
	Gabarito_600SemiBold,
	Gabarito_700Bold,
	Gabarito_800ExtraBold,
	Gabarito_900Black,
} from "@expo-google-fonts/gabarito";
import * as SystemUI from "expo-system-ui";
import * as SplashScreen from "expo-splash-screen";
import { useRouter } from "expo-router";
import { useUserStatus } from "./useUserStatus";
import { AUTH_ROUTES, TAB_ROUTES } from "@/constants/routes";

export interface UseInitialSetupReturn {
	isReady: boolean;
}

/**
 * Custom hook that handles initial app setup including font loading, system UI configuration, and navigation.
 */
export const useInitialSetup = (): UseInitialSetupReturn => {
	const router = useRouter();
	const { isNewUser, isLoading } = useUserStatus();

	const [fontsLoaded] = useFonts({
		Inter: require("@tamagui/font-inter/otf/Inter-Medium.otf"),
		InterBold: require("@tamagui/font-inter/otf/Inter-Bold.otf"),
	});

	const [gabaritoFontsLoaded] = useGabaritoFonts({
		Gabarito_400Regular,
		Gabarito_500Medium,
		Gabarito_600SemiBold,
		Gabarito_700Bold,
		Gabarito_800ExtraBold,
		Gabarito_900Black,
	});

	const isReady = fontsLoaded && gabaritoFontsLoaded;

	// Configure system UI for light content
	useEffect(() => {
		const configureSystemUI = async () => {
			if (Platform.OS === "android") {
				await SystemUI.setBackgroundColorAsync("#00151B");
			}
		};

		configureSystemUI();
	}, []);

	useEffect(() => {
		if (isReady) {
			// Handle user status navigation once initial setup is complete
			if (!isLoading && isNewUser !== null) {
				SplashScreen.hideAsync();
				if (isNewUser) {
					router.replace(AUTH_ROUTES.WELCOME);
				} else {
					router.replace(TAB_ROUTES.HOME);
				}
			}
		}
	}, [isReady, isLoading, isNewUser, router]);

	return {
		isReady,
	};
};
