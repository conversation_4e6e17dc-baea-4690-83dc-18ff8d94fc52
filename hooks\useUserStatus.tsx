import { useState, useEffect, useCallback } from "react";
import * as SecureStore from "expo-secure-store";

const USER_STATUS_KEY = "chatfeed_user_status";

export interface UseUserStatusReturn {
	isNewUser: boolean | null;
	isLoading: boolean;
	markUserAsReturning: () => Promise<void>;
}

/**
 * Custom hook to determine if a user is new or returning based on stored user data.
 * Uses SecureStore to securely persist user status across app sessions.
 */
export const useUserStatus = (): UseUserStatusReturn => {
	const [isNewUser, setIsNewUser] = useState<boolean | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	const loadUserStatus = useCallback(async () => {
		try {
			setIsLoading(true);
			const storedStatus = await SecureStore.getItemAsync(USER_STATUS_KEY);

			if (storedStatus === null) {
				// First time user - no stored status found
				setIsNewUser(true);
			} else {
				// Returning user - status found in storage
				const parsedStatus = JSON.parse(storedStatus);
				setIsNewUser(parsedStatus.isNewUser ?? true);
			}
		} catch (error) {
			console.error("Error loading user status:", error);
			// Default to new user if there's an error
			setIsNewUser(true);
		} finally {
			setIsLoading(false);
		}
	}, []);

	const markUserAsReturning = useCallback(async () => {
		try {
			const userStatus = {
				isNewUser: false,
				completedOnboarding: true,
			};

			await SecureStore.setItemAsync(
				USER_STATUS_KEY,
				JSON.stringify(userStatus),
			);
			setIsNewUser(false);
		} catch (error) {
			console.error("Error marking user as returning:", error);
		}
	}, []);

	// Load user status on mount
	useEffect(() => {
		loadUserStatus();
	}, [loadUserStatus]);

	return {
		isNewUser,
		isLoading,
		markUserAsReturning,
	};
};
