import { QueryClient } from "@tanstack/react-query";

export function createQueryClient(): QueryClient {
	return new QueryClient({
		defaultOptions: {
			queries: {
				staleTime: 1000 * 60 * 5, // 5 minutes
				gcTime: 1000 * 60 * 10, // 10 minutes
				retry: 2,
				retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
				refetchOnWindowFocus: false, // Don't refetch on window focus by default (will be managed by app state)
				networkMode: "online", // Network mode - fail fast when offline
			},
			mutations: {
				retry: 1,
				networkMode: "online", // Network mode for mutations
			},
		},
	});
}

// Use this for accessing the client outside of React components
export const queryClient = createQueryClient();
