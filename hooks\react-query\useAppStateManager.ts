import { useEffect } from "react";
import { AppState, Platform } from "react-native";
import { focusManager } from "@tanstack/react-query";

// type
import type { AppStateStatus } from "react-native";

/**
 * Hook to manage React Query's focus state using React Native's AppState
 * This enables refetching when the app comes to the foreground
 * https://tanstack.com/query/v4/docs/framework/react/guides/window-focus-refetching#managing-focus-in-react-native
 */
export function useAppStateManager() {
	useEffect(() => {
		function onAppStateChange(status: AppStateStatus) {
			// Only manage focus for native platforms, not web
			if (Platform.OS !== "web") {
				focusManager.setFocused(status === "active");
			}
		}

		// Set initial state
		onAppStateChange(AppState.currentState);

		// Listen for app state changes
		const subscription = AppState.addEventListener("change", onAppStateChange);

		return () => subscription.remove();
	}, []);
}
