import { useState } from "react";
import { FlatList, type ListRenderItem } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { YStack, Spinner } from "tamagui";
import { PostCard } from "./PostCard";
import { Button } from "../atoms";
import { Tooltip } from "../molecules";
import { useTooltip } from "@/hooks";

// type
import type { PostData } from "@/types/post";

export interface PostFeedProps {
	data: PostData[];
	isLoading?: boolean;
}

export const PostFeed = ({ data, isLoading = false }: PostFeedProps) => {
	const insets = useSafeAreaInsets();
	const [isLoadingMore] = useState(false);
	const { showTooltip, triggerTooltip } = useTooltip();

	const onLoadMore = () => {
		triggerTooltip();
	};

	// Loading state UI
	if (isLoading) {
		return (
			<YStack
				flex={1}
				alignItems="center"
				justifyContent="center"
				paddingTop={136 + insets.top}
				paddingBottom={140 + insets.bottom}
			>
				<Spinner size="large" color="white" />
			</YStack>
		);
	}

	const renderItem: ListRenderItem<PostData> = ({ item }) => (
		<PostCard post={item} />
	);

	const keyExtractor = (item: PostData) => item.id;

	const renderFooter = () => {
		return (
			<YStack alignItems="center" paddingVertical={16}>
				<Tooltip
					visible={showTooltip}
					position="bottom"
					containerStyle={{
						position: "absolute",
						top: -20,
						left: -100,
					}}
				>
					Login to interact with posts
				</Tooltip>
				<Button
					title={isLoadingMore ? undefined : "Load More"}
					onPress={onLoadMore}
					disabled={isLoadingMore}
					validBackgroundColor="white"
					isActive={!isLoadingMore}
				>
					{isLoadingMore ? <Spinner size="small" color="white" /> : null}
				</Button>
			</YStack>
		);
	};

	return (
		<FlatList
			data={data}
			renderItem={renderItem}
			keyExtractor={keyExtractor}
			ListFooterComponent={renderFooter}
			contentContainerStyle={{
				paddingTop: 136 + insets.top,
				paddingHorizontal: 20,
				paddingBottom: 140 + insets.bottom,
				gap: 16,
			}}
			showsVerticalScrollIndicator={false}
			removeClippedSubviews={true}
			maxToRenderPerBatch={5}
			updateCellsBatchingPeriod={50}
			initialNumToRender={3}
			windowSize={10}
		/>
	);
};
