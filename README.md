# TouChat-UI

A React Native chat feed UI built with Expo, Tamagui, and React Navigation.

## 📦 Package Manager

This project uses [Bun](https://bun.sh/) as the package manager for faster installation and better performance.

## 🛠️ Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd <project folder>
   ```

2. **Install dependencies**

   ```bash
   bun install
   ```

## 🚀 Running the Application

### Development Server

Start the Expo development server:

```bash
bun start
```

This will open the Expo DevTools in your browser where you can:

- Scan the QR code with the Expo Go app on your mobile device
- Press `i` to open iOS Simulator
- Press `a` to open Android Emulator
- Press `w` to open in web browser

### Platform-specific Commands

```bash
# Run on iOS Simulator
bun ios

# Run on Android Emulator
bun android

# Run on Web
bun web
```

## 📋 Additional Commands

```bash
# Add a new dependency
bun add <package-name>

# Add a development dependency
bun add -d <package-name>

# Remove a dependency
bun remove <package-name>

# Update dependencies
bun update

# Run linting
bun lint
```

## 🔗 Useful Links

- [Bun Documentation](https://bun.sh/docs)
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Tamagui Documentation](https://tamagui.dev/)
- [Expo Router Documentation](https://expo.github.io/router/)
- [React Navigation Documentation](https://reactnavigation.org/)
