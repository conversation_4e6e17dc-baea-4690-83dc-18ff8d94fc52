import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResol<PERSON> } from "@hookform/resolvers/zod";
import { YStack, XStack, Separator } from "tamagui";
import { LogoReddit, LogoTwitter, LogoTelegram } from "@/assets/svg";
import { Text, Input, Button } from "@/components";
import { loginFormSchema, type LoginFormData } from "@/schemas/auth";

export default function LoginOrSignUpScreen() {
	const {
		control,
		handleSubmit,
		formState: { errors, isValid, isSubmitted },
	} = useForm<LoginFormData>({
		resolver: zodResolver(loginFormSchema),
		mode: "onChange",
		defaultValues: {
			email: "",
		},
	});

	const onSubmit = (_data: LoginFormData) => {
		// TODO: Handle form submission
	};

	return (
		<YStack
			flex={1}
			justifyContent="center"
			alignItems="center"
			p={16}
			gap={16}
			minHeight="100%"
		>
			<Text fontSize={36} fontWeight={600}>
				Login or Sign Up
			</Text>
			<Text color="$gray6" fontSize={18} textAlign="center">
				Join the conversation and get personalized crypto and blockchain
				insights
			</Text>
			<XStack gap={20} mt={10}>
				<LogoReddit height={48} width={48} />
				<LogoTwitter height={48} width={48} />
				<LogoTelegram height={48} width={48} />
			</XStack>
			<XStack mt={20} alignItems="center">
				<Separator borderColor="$gray3" />
				<Text marginHorizontal={12} fontWeight={600}>
					OR
				</Text>
				<Separator borderColor="$gray3" />
			</XStack>

			{/* Email Form */}
			<YStack gap={16} mt={16}>
				<Controller
					control={control}
					name="email"
					render={({ field: { onChange, onBlur, value } }) => (
						<Input
							placeholder="Email Address"
							value={value}
							onChangeText={(text: string) => onChange(text.trim())}
							onBlur={onBlur}
							hasError={isSubmitted && !isValid}
							errorMessage={errors.email?.message}
							keyboardType="email-address"
							autoCapitalize="none"
							autoComplete="email"
							width="100%"
						/>
					)}
				/>
				<Button
					isActive={isValid}
					disabled={!isValid}
					onPress={handleSubmit(onSubmit)}
					title="CONTINUE WITH EMAIL"
				/>
			</YStack>
			<Text color="$white1" fontSize={12} textAlign="center">
				By continuing, you agree to our{" "}
				<Text fontWeight={600} color={"$yellow"} textDecorationLine="underline">
					terms of Service
				</Text>{" "}
				and{" "}
				<Text fontWeight={600} color={"$yellow"} textDecorationLine="underline">
					privacy policy
				</Text>
			</Text>
		</YStack>
	);
}
