import { config } from "@tamagui/config/v3";
import { createTamagui } from "tamagui";
import { createAnimations } from "@tamagui/animations-moti";

const animations = createAnimations({
	bouncy: {
		type: "spring",
		damping: 10,
		mass: 0.9,
		stiffness: 100,
	},
	lazy: {
		type: "spring",
		damping: 20,
		stiffness: 60,
	},
	quick: {
		type: "spring",
		damping: 20,
		mass: 1.2,
		stiffness: 250,
	},
	smooth: {
		type: "timing",
		duration: 300,
	},
	slow: {
		type: "timing",
		duration: 500,
	},
});

// You can customize the config here or use the default
const appConfig = createTamagui({
	...config,
	animations,
	tokens: {
		...config.tokens,
		color: {
			...config.tokens.color,
			"bg-dark": "#00151B",
			primary: "#BBFBBD",
			black: "#121212",
			yellow: "#ffd000",
			orange: "#FEAA34",
			error: "#FF6B6B",
			"border-gray": "rgba(255, 255, 255, 0.08)",
			gray1: "rgba(255, 255, 255, 0.12)",
			gray2: "rgba(255, 255, 255, 0.24)",
			gray3: "rgba(255, 255, 255, 0.32)",
			gray4: "rgba(255, 255, 255, 0.40)",
			gray5: "rgba(255, 255, 255, 0.50)",
			gray6: "rgba(255, 255, 255, 0.56)",
			gray7: "rgba(255, 255, 255, 0.70)",
		},
	},
});

export default appConfig;

export type Conf = typeof appConfig;

declare module "tamagui" {
	// eslint-disable-next-line @typescript-eslint/no-empty-object-type
	interface TamaguiCustomConfig extends Conf {}
}
