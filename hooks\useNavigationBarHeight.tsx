import { Platform } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

/**
 * Custom hook to detect Android navigation bar height
 * Returns the navigation bar height for Android devices, 0 for iOS
 */
export const useNavigationBarHeight = () => {
	const insets = useSafeAreaInsets();

	let navigationBarHeight = 0;

	if (Platform.OS === "android") {
		// The bottom inset represents the navigation bar height on Android
		// This will be 0 if gesture navigation is enabled (no navigation bar)
		navigationBarHeight = insets.bottom;
	}

	return {
		navigationBarHeight,
		insets,
	};
};
