import { KeyboardAvoidingView, Platform } from "react-native";
import { Slot } from "expo-router";
import { YStack, Image, ScrollView } from "tamagui";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import AuthBackground from "@/assets/images/auth-bg.png";

export default function AuthLayout() {
	const insets = useSafeAreaInsets();

	return (
		<YStack flex={1} position="relative" backgroundColor="$bg-dark">
			<Image // Background Image
				source={AuthBackground}
				position="absolute"
				top={0}
				left={0}
				right={0}
				bottom={0}
				objectFit="cover"
				zIndex={0}
				height={"100%"}
				width={"100%"}
			/>
			<KeyboardAvoidingView
				style={{ flex: 1 }}
				behavior={Platform.OS === "ios" ? "padding" : "height"}
				keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 10}
			>
				<ScrollView
					flex={1}
					contentContainerStyle={{
						flexGrow: 1,
						paddingTop: insets.top,
						paddingBottom: Math.max(insets.bottom, 20),
					}}
					keyboardShouldPersistTaps="handled"
					showsVerticalScrollIndicator={false}
				>
					<Slot />
				</ScrollView>
			</KeyboardAvoidingView>
		</YStack>
	);
}
