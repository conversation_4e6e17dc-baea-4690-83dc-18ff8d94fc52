import { PostImage } from "./PostImage";
import { PostVideo } from "./PostVideo";

export interface MediaData {
	type: "image" | "video";
	src: string;
}

export interface PostMediaProps {
	media?: MediaData;
	alt?: string;
	aspectRatio?: number;
	showLoadingSpinner?: boolean;
	showErrorMessage?: boolean;
	onVideoPress?: () => void;
	mt?: number;
}

export const PostMedia = ({
	media,
	alt,
	aspectRatio = 16 / 9,
	showLoadingSpinner = true,
	showErrorMessage = true,
	onVideoPress,
	mt,
}: PostMediaProps) => {
	if (!media) {
		return null;
	}

	const styleProps = {
		mt,
	};

	if (media.type === "image") {
		return (
			<PostImage
				src={media.src}
				alt={alt || "Post image"}
				aspectRatio={aspectRatio}
				showLoadingSpinner={showLoadingSpinner}
				showErrorMessage={showErrorMessage}
				{...styleProps}
			/>
		);
	}

	if (media.type === "video") {
		return (
			<PostVideo
				src={media.src}
				alt={alt || "Post video"}
				aspectRatio={aspectRatio}
				showLoadingSpinner={showLoadingSpinner}
				showErrorMessage={showErrorMessage}
				onPress={onVideoPress}
				{...styleProps}
			/>
		);
	}

	return null;
};
