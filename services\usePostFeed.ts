import { useQuery } from "@tanstack/react-query";
// import { api } from '@/config';

// constants
import { SAMPLE_POSTS } from "@/constants/samplePosts";

export const postFeedKeys = ["postFeed"] as const;

export const usePostFeed = () => {
	return useQuery({
		queryKey: postFeedKeys,
		queryFn: async (): Promise<any> => {
			// const response = await api.get('/homefeed/v1/posts');

			// if (!response.ok) {
			//     const errorMessage = response.problem || 'Unknown error';
			//     throw new Error(`Failed to fetch posts: ${errorMessage}`);
			// }

			return SAMPLE_POSTS;
		},
	});
};
