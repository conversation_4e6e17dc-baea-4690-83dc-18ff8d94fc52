import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "moti";
import { styled, XStack } from "tamagui";
import { FontAwesome6 } from "@expo/vector-icons";

// types
import type React from "react";

export interface CheckboxProps {
	checked?: boolean;
	checkedColors?: string;
	uncheckedColors?: string;
	borderColor?: string;
	checkIconColor?: string;
}

const CheckboxContainer = styled(XStack, {
	alignItems: "center",
	justifyContent: "center",
	width: 20,
	height: 20,
	borderRadius: 4,
});

export const Checkbox: React.FC<CheckboxProps> = ({
	checked = false,
	checkedColors = "$primary",
	uncheckedColors = "transparent",
	borderColor = "white",
	checkIconColor = "black",
}) => {
	return (
		<CheckboxContainer
			backgroundColor={checked ? checkedColors : uncheckedColors}
			borderColor={borderColor}
			borderWidth={checked ? 0 : 1}
		>
			<MotiView
				animate={{
					opacity: checked ? 1 : 0,
					scale: checked ? 1 : 0.5,
				}}
				transition={{
					type: "timing",
					duration: 200,
				}}
			>
				<FontAwesome6 name="check" size={14} color={checkIconColor} />
			</MotiView>
		</CheckboxContainer>
	);
};
