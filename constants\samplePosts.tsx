import type { PostData } from "@/types/post";

export const SAMPLE_POSTS: PostData[] = [
	{
		id: "post-1",
		user_screen_name: "<PERSON>",
		user_handle: "@cryptomarco",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://x.com/cryptomarco",
		channel_name: "X Post",
		source: "twitter",
		source_post_url: "https://x.com/cryptomarco/status/post-1",
		source_post_id: "post-1",
		reply_count: 89,
		repost_count: 45,
		reaction_count: {
			heart: 200,
			votes: 0,
			likes: 1800,
		},
		view_count: 12400,
		sentiment_tags: ["crypto", "bitcoin", "adoption"],
		media: [
			{
				url: "https://images.unsplash.com/photo-*************-5a555bb7020d?w=800&h=600&fit=crop",
				caption: "Bitcoin adoption in emerging markets visualization",
			},
		],
		post_text:
			"Been studying Bitcoin adoption patterns across emerging markets. What I'm seeing in Latin America and Africa is incredible - people choosing Bitcoin as their primary savings vehicle over traditional banking. The revolution is happening quietly.",
		created_at: *************,
	},
	{
		id: "post-2",
		user_screen_name: "Sarah Kim",
		user_handle: "u/sarahbuilds",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-*************-9aa86183ac98?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://reddit.com/r/CryptoDev",
		channel_name: "r/CryptoDev",
		source: "reddit",
		source_post_url: "https://reddit.com/r/CryptoDev/comments/post-2",
		source_post_id: "post-2",
		reply_count: 156,
		repost_count: 32,
		reaction_count: {
			heart: 120,
			votes: 892,
			likes: 0,
		},
		view_count: 8200,
		sentiment_tags: ["NFT", "BNBChain", "crypto"],
		media: [],
		post_text:
			"🚀 Exciting News! We've just deployed our NFT contracts on the BNB Chain🎉 By simply holding our NFTs, you can mine $BYTC passively! Check out our contract on BscScan.",
		created_at: *************,
	},
	{
		id: "post-3",
		user_screen_name: "Alex Rodriguez",
		user_handle: "@alexbuilds",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://x.com/alexbuilds",
		channel_name: "X Post",
		source: "twitter",
		source_post_url: "https://x.com/alexbuilds/status/post-3",
		source_post_id: "post-3",
		reply_count: 234,
		repost_count: 67,
		reaction_count: {
			heart: 300,
			votes: 0,
			likes: 2100,
		},
		view_count: 15700,
		sentiment_tags: ["Solana", "dev", "superteam"],
		media: [
			{
				url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800&h=600&fit=crop",
				caption: "Solana development ecosystem",
			},
		],
		post_text:
			"Building on Solana? Superteam is a cheat code. The ecosystem support and resources they provide are incredible for developers.",
		created_at: 1756201016000,
	},
	{
		id: "post-4",
		user_screen_name: "Emma Wilson",
		user_handle: "@emmaweb3",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://t.me/web3developers",
		channel_name: "Web3 Developers",
		source: "telegram",
		source_post_url: "https://t.me/web3developers/post-4",
		source_post_id: "post-4",
		reply_count: 145,
		repost_count: 28,
		reaction_count: {
			heart: 80,
			votes: 0,
			likes: 1200,
		},
		view_count: 9300,
		sentiment_tags: ["web3", "zk", "privacy"],
		media: [],
		post_text:
			"Web3 development is evolving so fast! Just shipped a new dApp with zero-knowledge proofs. The future of privacy-preserving applications is here. 🔐✨",
		created_at: 1755941912000,
	},
	{
		id: "post-5",
		user_screen_name: "CryptoWhale",
		user_handle: "@cryptowhale",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://x.com/cryptowhale",
		channel_name: "X Post",
		source: "twitter",
		source_post_url: "https://x.com/cryptowhale/status/post-5",
		source_post_id: "post-5",
		reply_count: 187,
		repost_count: 89,
		reaction_count: {
			heart: 400,
			votes: 0,
			likes: 2300,
		},
		view_count: 18700,
		sentiment_tags: ["NFT", "BNBChain", "passive income"],
		media: [],
		post_text:
			"🚀 Exciting News! We've just deployed our NFT contracts on the @BNBCHAIN🎉, By simply holding our NFTs, you can mine $BYTC passively! Check out our contract on BscScan.",
		created_at: 1755941912000,
	},
	{
		id: "post-6",
		user_screen_name: "BlockchainDev",
		user_handle: "u/blockdev",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://reddit.com/r/solana",
		channel_name: "r/solana",
		source: "reddit",
		source_post_url: "https://reddit.com/r/solana/comments/post-6",
		source_post_id: "post-6",
		reply_count: 156,
		repost_count: 24,
		reaction_count: {
			heart: 120,
			votes: 892,
			likes: 0,
		},
		view_count: 8200,
		sentiment_tags: ["Solana", "superteam", "dev"],
		media: [
			{
				url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800&h=600&fit=crop",
				caption: "Solana blockchain development",
			},
		],
		post_text:
			"Building on Solana? Superteam is a cheat code. The ecosystem support and resources they provide are incredible for developers.",
		created_at: *************,
	},
	{
		id: "post-7",
		user_screen_name: "DeFi Hunter",
		user_handle: "@defihunter",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://x.com/defihunter",
		channel_name: "X Post",
		source: "twitter",
		source_post_url: "https://x.com/defihunter/status/post-7",
		source_post_id: "post-7",
		reply_count: 234,
		repost_count: 156,
		reaction_count: {
			heart: 200,
			votes: 0,
			likes: 1500,
		},
		view_count: 12400,
		sentiment_tags: ["BTC", "whale", "crash"],
		media: [
			{
				url: "https://images.unsplash.com/photo-*************-5a555bb7020d?w=800&h=600&fit=crop",
				caption: "Bitcoin market analysis chart",
			},
		],
		post_text:
			'🚨 @Bitcoin crashes to $115K after Galaxy Digital moves 12K BTC ($1.39B) to exchanges. @spotonchain links the transfer to a possible 80K+ BTC dump by a legendary "ancient whale." 🐋 Is this the start of a deeper dip? 👀 #BTC #BTTF #Crypto #WhaleAlert #CryptoCrash',
		created_at: *************,
	},
	{
		id: "post-8",
		user_screen_name: "NFT Collector",
		user_handle: "@nftcollector",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://t.me/nftcommunity",
		channel_name: "NFT Community",
		source: "telegram",
		source_post_url: "https://t.me/nftcommunity/post-8",
		source_post_id: "post-8",
		reply_count: 78,
		repost_count: 15,
		reaction_count: {
			heart: 40,
			votes: 0,
			likes: 567,
		},
		view_count: 4800,
		sentiment_tags: ["NFT", "meme", "doge"],
		media: [
			{
				url: "https://images.unsplash.com/photo-1640340434855-6084b1f4901c?w=800&h=600&fit=crop",
				caption: "NFT digital art collection",
			},
		],
		post_text:
			"We all love $Cocoro because it's part of Kabosu history that created Doge culture and meme space. Also Neiro is the same family. We need to respect and honor this legacy. 🖤🖤",
		created_at: *************,
	},
	{
		id: "post-9",
		user_screen_name: "Web3 Builder",
		user_handle: "@web3builder",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://x.com/web3builder",
		channel_name: "X Post",
		source: "twitter",
		source_post_url: "https://x.com/web3builder/status/post-9",
		source_post_id: "post-9",
		reply_count: 298,
		repost_count: 124,
		reaction_count: {
			heart: 600,
			votes: 0,
			likes: 3100,
		},
		view_count: 25600,
		sentiment_tags: ["TRON", "AfroToken", "web3"],
		media: [
			{
				url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800&h=600&fit=crop",
				caption: "Web3 and blockchain technology",
			},
		],
		post_text:
			'Suited up for success and shouting out loud "I love @AfroTokenSUN" because really utility and culture can coexist, welcome to the $Afro & TRON Eco Era @justinsuntron',
		created_at: *************,
	},
	{
		id: "post-10",
		user_screen_name: "DeFi Researcher",
		user_handle: "u/defiresearcher",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://reddit.com/r/DeFi",
		channel_name: "r/DeFi",
		source: "reddit",
		source_post_url: "https://reddit.com/r/DeFi/comments/post-10",
		source_post_id: "post-10",
		reply_count: 89,
		repost_count: 23,
		reaction_count: {
			heart: 45,
			votes: 567,
			likes: 0,
		},
		view_count: 6800,
		sentiment_tags: ["DeFi", "yield", "farming", "analysis"],
		media: [],
		post_text:
			"Deep dive into yield farming strategies for 2025: After analyzing 50+ protocols, here are the top risk-adjusted opportunities. Remember: DYOR and never invest more than you can afford to lose. 📊💰",
		created_at: *************,
	},
	{
		id: "post-11",
		user_screen_name: "Crypto Analyst",
		user_handle: "@cryptoanalyst",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://x.com/cryptoanalyst",
		channel_name: "X Post",
		source: "twitter",
		source_post_url: "https://x.com/cryptoanalyst/status/post-11",
		source_post_id: "post-11",
		reply_count: 167,
		repost_count: 89,
		reaction_count: {
			heart: 320,
			votes: 0,
			likes: 2400,
		},
		view_count: 18900,
		sentiment_tags: ["ethereum", "layer2", "scaling"],
		media: [
			{
				url: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800&h=600&fit=crop",
				caption: "Ethereum Layer 2 scaling solutions",
			},
		],
		post_text:
			"Layer 2 solutions are finally hitting their stride! Arbitrum and Optimism are processing more transactions than ever, with fees dropping to cents. The Ethereum scaling roadmap is working exactly as planned. 🚀⚡",
		created_at: 1756028312000,
	},
	{
		id: "post-12",
		user_screen_name: "GameFi Dev",
		user_handle: "u/gamefidev",
		user_profile_pic_url:
			"https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face",
		channel_url: "https://reddit.com/r/GameFi",
		channel_name: "r/GameFi",
		source: "reddit",
		source_post_url: "https://reddit.com/r/GameFi/comments/post-12",
		source_post_id: "post-12",
		reply_count: 203,
		repost_count: 45,
		reaction_count: {
			heart: 89,
			votes: 1234,
			likes: 0,
		},
		view_count: 11200,
		sentiment_tags: ["gaming", "NFT", "play2earn"],
		media: [],
		post_text:
			"Just launched our first GameFi prototype! Players can earn tokens by completing quests and trading in-game NFTs. The future of gaming is here - where fun meets financial opportunity. Beta testing starts next week! 🎮💰",
		created_at: 1755855112000,
	},
];
