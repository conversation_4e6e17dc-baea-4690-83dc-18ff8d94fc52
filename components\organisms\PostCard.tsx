import { <PERSON>, <PERSON>, Separator, YS<PERSON>ck, type CardProps } from "tamagui";
import { Text } from "../atoms";
import {
	PostHeader,
	UserInfo,
	EngagementMetrics,
	ActionBar,
	PostImage,
} from "../molecules";

// type
import type { PostData } from "@/types/post";

export interface PostCardProps extends Omit<CardProps, "children"> {
	post: PostData;
}

export const PostCard = ({ post, ...props }: PostCardProps) => {
	// Calculate total likes from reaction_count
	const totalLikes =
		post.reaction_count.heart +
		post.reaction_count.likes +
		post.reaction_count.votes;

	return (
		<YStack>
			<Card
				backgroundColor="#0a1e24"
				p={12}
				position="relative"
				borderTopColor="$gray2"
				borderTopWidth={1}
				borderBottomColor="$gray2"
				borderBottomWidth={1}
				{...props}
			>
				<View
					position="absolute"
					top={0}
					left={-1}
					right={-1}
					bottom={0}
					zIndex={-1}
					backgroundColor="$gray1"
					borderRadius={10}
				/>
				<PostHeader
					platformName={post.source}
					channelName={post.channel_name}
				/>
				<Separator borderColor="$gray1" marginVertical={12} />
				<UserInfo
					avatar={post.user_profile_pic_url}
					name={post.user_screen_name}
					handle={post.user_handle}
					isReddit={post.source === "reddit"}
					timestamp={post.created_at}
				/>
				<Text fontSize={16} textAlign="justify">
					{post.post_text}
				</Text>
				{post.media.length > 0 ? (
					<PostImage
						src={post.media[0].url}
						alt={post.media[0].caption || "Post media"}
						mt={12}
					/>
				) : null}
				<EngagementMetrics
					likeCount={totalLikes}
					commentCount={post.reply_count}
					repostCount={post.repost_count}
				/>
			</Card>
			<ActionBar viewCount={post.view_count} id={post.id} />
		</YStack>
	);
};
