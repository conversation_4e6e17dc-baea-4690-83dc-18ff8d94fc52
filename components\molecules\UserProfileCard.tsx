import { Feather } from "@expo/vector-icons";
import { View, XStack, YStack } from "tamagui";

import { Text } from "../atoms";

interface UserProfileCardProps {
	cardHeader: string;
	cardSubheader: string;
	onPress?: () => void;
}

export const UserProfileCard = ({
	cardHeader,
	cardSubheader,
	onPress,
}: UserProfileCardProps) => {
	return (
		<XStack
			backgroundColor="$border-gray"
			height={"73px"}
			padding="$4"
			borderRadius="$4"
			alignItems="center"
			onPress={onPress}
		>
			<YStack flex={1} gap="$3">
				<Text color="$primary" fontWeight="bold" fontSize="$4">
					{cardHeader}
				</Text>
				<Text color="white" fontSize="$3">
					{cardSubheader}
				</Text>
			</YStack>

			<View
				backgroundColor="white"
				borderRadius={20}
				padding="$1"
				justifyContent="center"
				alignItems="center"
			>
				<Feather name="plus" size={15} color="$black" />
			</View>
		</XStack>
	);
};
