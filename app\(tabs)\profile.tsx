import { Feather } from "@expo/vector-icons";
import { useState } from "react";
import { ScrollView, Separator, View, XStack, YStack } from "tamagui";
import { Text, UserProfileAvatar, UserProfileCard } from "@/components";

// constants
import { PROFILE_COMPLETION_SECTIONS } from "@/constants/profile";

export default function ProfileTab() {
	const [userName] = useState("<PERSON> Jones");
	const [userHandle] = useState("@thecryptonerd");

	return (
		<ScrollView
			flex={1}
			backgroundColor="$bg-dark"
			contentContainerStyle={{ flexGrow: 1 }}
			py={"$4"}
		>
			<YStack flex={1} pt="$10" px="$4" gap="$4">
				<XStack gap={16}>
					<UserProfileAvatar
						// imageUrl to be assigned to the user's profile picture, this is a placeholder
						imageUrl={"https://picsum.photos/id/237/200/300"}
					/>
					<YStack flex={1} justifyContent="space-between">
						<Text fontSize={24} fontWeight={600}>
							{userName}
						</Text>
						<Text fontSize={18} fontWeight={400}>
							{userHandle}
						</Text>
					</YStack>
					<Feather name="edit" size={24} color="white" />
				</XStack>

				<View flex={1} width="100%" gap="$4">
					<Separator marginVertical={15} borderColor="$gray2" />
					<XStack justifyContent="space-between">
						<Text fontSize={16} fontWeight={600}>
							COMPLETE YOUR PROFILE
						</Text>
						<Text fontSize={14} fontWeight={400}>
							0/{PROFILE_COMPLETION_SECTIONS.length}
						</Text>
					</XStack>

					{PROFILE_COMPLETION_SECTIONS.map((section) => (
						<UserProfileCard
							key={section.header}
							cardHeader={section.header}
							cardSubheader={section.subheader}
							onPress={() => {
								console.log(`Pressed card ${section.header}`);
							}}
						/>
					))}
				</View>
			</YStack>
		</ScrollView>
	);
}
