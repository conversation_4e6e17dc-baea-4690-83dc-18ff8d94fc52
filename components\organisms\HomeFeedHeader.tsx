import { useState, Fragment } from "react";
import { Dimensions } from "react-native";
import { XStack, Image, YStack, View } from "tamagui";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LogoCF, FilterIcon } from "@/assets/svg";
import HeaderBg from "@/assets/images/home-feed-header-bg.png";
import { Button } from "../atoms/Button";
import { Tooltip } from "../molecules";
import { useTooltip } from "@/hooks";

const tabs = ["RECENT", "FOR YOU", "TRENDING"] as const;

type Tab = (typeof tabs)[number];

const hasNotification = true;
export const HomeFeedHeader = () => {
	const insets = useSafeAreaInsets();
	const [activeTab, setActiveTab] = useState<Tab>("RECENT");
	const { showTooltip, triggerTooltip } = useTooltip();

	const windowWidth = Dimensions.get("window").width;

	const handleTabPress = (tab: Tab) => {
		setActiveTab(tab);

		if (tab !== "RECENT") {
			triggerTooltip();
		}
	};

	return (
		<>
			<YStack
				position="absolute"
				top={0}
				left={0}
				right={0}
				zIndex={0}
				backgroundColor={"$gray3"}
				height={125 + insets.top}
			/>
			<BlurView
				intensity={16}
				tint="dark"
				experimentalBlurMethod="dimezisBlurView"
				style={{
					paddingTop: insets.top,
					height: 125 + insets.top,
					backgroundColor: "transparent",
					position: "absolute",
					top: 0,
					left: 0,
					right: 0,
					zIndex: 50,
				}}
			/>
			<Image
				src={HeaderBg}
				position="absolute"
				top={0}
				left={0}
				right={0}
				height={125 + insets.top}
				opacity={0.65}
				zIndex={51}
			/>
			<YStack
				height={125 + insets.top}
				borderBottomColor={"$border-gray"}
				borderBottomWidth={1}
				pt={insets.top}
				pb={4}
				position="absolute"
				top={0}
				left={0}
				right={0}
				zIndex={52}
				backgroundColor="$colorTransparent"
			>
				<XStack flex={1} margin={20}>
					<LogoCF height={48} width={48} />
					<Ionicons
						name="settings-sharp"
						size={24}
						color="white"
						style={{ marginLeft: "auto" }}
					/>
					<View position="relative" marginLeft={20}>
						<MaterialCommunityIcons name="bell" size={24} color="white" />
						{hasNotification ? (
							<View
								position="absolute"
								top={1}
								right={1}
								backgroundColor="#FF3B30"
								borderRadius={99}
								borderWidth={2}
								borderColor="#172f30"
								width={10}
								height={10}
							/>
						) : null}
					</View>
				</XStack>
				<XStack
					flex={1}
					alignItems="center"
					pl={4}
					pb={4}
					gap={4}
					position="relative"
				>
					{tabs.map((tab) => {
						if (tab === "RECENT") {
							return (
								<Button
									key={tab}
									title={tab}
									flex={1}
									borderRadius={8}
									height={45}
									padding={10}
									backgroundColor={"$orange"}
									isActive
									onPress={() => handleTabPress(tab)}
								/>
							);
						}
						return (
							<Fragment key={tab}>
								<Tooltip
									visible={showTooltip && activeTab === tab}
									position="top"
									containerStyle={{
										position: "absolute",
										top: 26,
										left: windowWidth / (tab === "FOR YOU" ? 3.8 : 2),
									}}
								>
									Cooming Soon!
								</Tooltip>
								<Button
									title={tab}
									flex={1}
									borderRadius={8}
									height={45}
									padding={10}
									invalidTextColor={"$gray2"}
									backgroundColor={"$colorTransparent"}
									isActive={false}
									onPress={() => handleTabPress(tab)}
								/>
							</Fragment>
						);
					})}
					<FilterIcon style={{ marginHorizontal: 20 }} />
				</XStack>
			</YStack>
		</>
	);
};
