import { ProgressStepper } from "@/components";
import { Slot, Stack } from "expo-router";
import { Button, XStack } from "tamagui";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

export default function OnBoardingLayout() {
	const { top, bottom } = useSafeAreaInsets();

	return (
		<Stack
			screenOptions={{
				header: ({ navigation }) => {
					return (
						<XStack pt={top} mt={20} height={50} alignItems="center" gap={"$2"}>
							<Button
								onPress={() => navigation.goBack()}
								size={"$2"}
								icon={
									<Ionicons name="arrow-back-sharp" size={24} color="white" />
								}
								unstyled
								mt={2}
								pl={0}
							/>
							<ProgressStepper totalSteps={2} currentStep={1} />
						</XStack>
					);
				},
				contentStyle: {
					backgroundColor: "#101010",
					paddingHorizontal: 16,
					paddingBottom: bottom || 16,
				},
			}}
		>
			<Slot />
		</Stack>
	);
}
