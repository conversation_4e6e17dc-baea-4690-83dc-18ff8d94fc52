import { GestureHandlerRootView } from "react-native-gesture-handler";
import { Slot } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { TamaguiProvider } from "tamagui";
import { StatusBar } from "expo-status-bar";

import config from "../tamagui.config";
import { QueryProvider } from "@/providers";
import { useInitialSetup } from "@/hooks";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
	const { isReady } = useInitialSetup();

	if (!isReady) {
		return null;
	}

	return (
		<QueryProvider>
			<GestureHandlerRootView style={{ flex: 1 }}>
				<TamaguiProvider config={config}>
					<StatusBar style="light" />
					<Slot />
				</TamaguiProvider>
			</GestureHandlerRootView>
		</QueryProvider>
	);
}
