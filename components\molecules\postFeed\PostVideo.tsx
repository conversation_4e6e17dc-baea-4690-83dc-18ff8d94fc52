import { YStack, Text, Spinner, type YStackProps, Image } from "tamagui";
import { useState, useEffect } from "react";
import { TouchableOpacity } from "react-native";
import { getThumbnailAsync } from "expo-video-thumbnails";

export interface PostVideoProps extends Omit<YStackProps, "children"> {
	src: string;
	thumbnailSrc?: string;
	alt?: string;
	aspectRatio?: number;
	showLoadingSpinner?: boolean;
	showErrorMessage?: boolean;
	onPress?: () => void;
}

export const PostVideo = ({
	src,
	thumbnailSrc,
	alt = "Post video",
	aspectRatio = 16 / 9,
	showLoadingSpinner = true,
	showErrorMessage = true,
	onPress,
	width = "100%",
	...props
}: PostVideoProps) => {
	const [isLoading, setIsLoading] = useState(true);
	const [hasError, setHasError] = useState(false);
	const [thumbnailUri, setThumbnailUri] = useState<string | null>(null);

	// Generate thumbnail
	useEffect(() => {
		const generateThumbnail = async () => {
			if (thumbnailSrc) {
				setThumbnailUri(thumbnailSrc);
				setIsLoading(false);
				return;
			}

			try {
				setIsLoading(true);
				const { uri } = await getThumbnailAsync(src, {
					time: 1000, // Get thumbnail at 1 second
					quality: 0.8,
				});
				setThumbnailUri(uri);
				setHasError(false);
			} catch (error) {
				console.warn("Failed to generate video thumbnail:", error);
				setHasError(true);
			} finally {
				setIsLoading(false);
			}
		};

		generateThumbnail();
	}, [src, thumbnailSrc]);

	const handleImageLoad = () => {
		setIsLoading(false);
		setHasError(false);
	};

	const handleImageError = () => {
		setIsLoading(false);
		setHasError(true);
	};

	const handlePress = () => {
		if (onPress) {
			onPress();
		}
	};

	if (hasError && showErrorMessage) {
		return (
			<YStack
				width={width}
				aspectRatio={aspectRatio}
				backgroundColor="$black"
				alignItems="center"
				justifyContent="center"
				{...props}
			>
				<Text color="$gray7">Failed to load video</Text>
			</YStack>
		);
	}

	return (
		<TouchableOpacity onPress={handlePress} activeOpacity={0.9}>
			<YStack position="relative" width={width} {...props}>
				{isLoading && showLoadingSpinner ? (
					<YStack
						position="absolute"
						top={0}
						left={0}
						right={0}
						bottom={0}
						alignItems="center"
						justifyContent="center"
						backgroundColor="$black"
						zIndex={1}
					>
						<Spinner size="large" color="white" />
					</YStack>
				) : null}

				{thumbnailUri ? (
					<Image
						source={{ uri: thumbnailUri }}
						width="100%"
						aspectRatio={aspectRatio}
						onLoad={handleImageLoad}
						onError={handleImageError}
						accessibilityLabel={alt}
					/>
				) : null}

				{/* Play Button Overlay */}
				<YStack
					position="absolute"
					top={0}
					left={0}
					right={0}
					bottom={0}
					alignItems="center"
					justifyContent="center"
					zIndex={2}
				>
					<YStack
						backgroundColor="rgba(0, 0, 0, 0.6)"
						borderRadius={50}
						width={60}
						height={60}
						alignItems="center"
						justifyContent="center"
					>
						<Text color="white" fontSize={36} fontWeight="bold" mb={14} ml={6}>
							▶
						</Text>
					</YStack>
				</YStack>
			</YStack>
		</TouchableOpacity>
	);
};
