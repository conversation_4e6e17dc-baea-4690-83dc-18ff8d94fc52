import {
	differenceInMinutes,
	differenceInHours,
	differenceInDays,
	differenceInWeeks,
	differenceInMonths,
	differenceInYears,
} from "date-fns";

/**
 * Formats a Unix timestamp to relative time string
 * @param timestamp - Unix timestamp in milliseconds
 * @returns Formatted relative time string (e.g., "2h", "5d", "3m")
 */
export const formatRelativeTime = (timestamp: number): string => {
	const now = new Date();
	const date = new Date(timestamp);

	// Handle invalid timestamps
	if (
		!timestamp ||
		timestamp <= 0 ||
		Number.isNaN(timestamp) ||
		Number.isNaN(date.getTime())
	) {
		return "now";
	}

	const minutes = differenceInMinutes(now, date);
	const hours = differenceInHours(now, date);
	const days = differenceInDays(now, date);
	const weeks = differenceInWeeks(now, date);
	const months = differenceInMonths(now, date);
	const years = differenceInYears(now, date);

	if (minutes < 1) {
		return "now";
	} else if (minutes < 60) {
		return `${minutes}m`;
	} else if (hours < 24) {
		return `${hours}h`;
	} else if (days < 7) {
		return `${days}d`;
	} else if (weeks < 4) {
		return `${weeks}w`;
	} else if (months < 12) {
		return `${months}M`;
	} else {
		return `${years}y`;
	}
};

/**
 * Abbreviates a number into a human-readable string with K/M suffixes
 * @param count - The number to abbreviate
 * @returns Abbreviated string with K/M suffixes (e.g., 1500 → "1.5K", 2300000 → "2.3M")
 */
export const abbreviateNumber = (count: number): string => {
	// Handle edge cases
	if (count < 0) return "0";
	if (count === 0) return "0";
	if (!Number.isFinite(count)) return "0";

	// Numbers below 1,000 - return as-is
	if (count < 1000) {
		return count.toString();
	}

	// Numbers 1,000 to 999,999 - use K suffix
	if (count < 1000000) {
		const thousands = count / 1000;
		// Round to 1 decimal place, but remove .0 if present
		const formatted = Math.round(thousands * 10) / 10;
		return formatted % 1 === 0 ? `${formatted.toFixed(0)}K` : `${formatted}K`;
	}

	// Numbers 1,000,000 and above - use M suffix
	const millions = count / 1000000;
	// Round to 1 decimal place, but remove .0 if present
	const formatted = Math.round(millions * 10) / 10;
	return formatted % 1 === 0 ? `${formatted.toFixed(0)}M` : `${formatted}M`;
};
