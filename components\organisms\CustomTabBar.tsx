import { TouchableOpacity } from "react-native";
import { BlurView } from "expo-blur";
import { useRouter } from "expo-router";
import { Image, View } from "tamagui";
import { LinearGradient } from "tamagui/linear-gradient";
import { useNavigationBarHeight } from "@/hooks/useNavigationBarHeight";
import { Button } from "../atoms";

// assets
import TabBg from "@/assets/images/tab-bg.png";

// constants
import { AUTH_ROUTES } from "@/constants/routes";

// type
import type { BottomTabBarProps } from "@react-navigation/bottom-tabs";

export const CustomTabBar = (props: BottomTabBarProps) => {
	const router = useRouter();
	const { navigationBarHeight } = useNavigationBarHeight();

	const handleLoginOrSignUp = () => {
		router.push(AUTH_ROUTES.LOGIN_OR_SIGN_UP);
	};
	return (
		<LinearGradient
			colors={["#00151bff", "#00151be5", "#00151b91", "#00151B00"]}
			start={{ x: 1, y: 1 }}
			end={{ x: 1, y: 0 }}
			style={{
				position: "absolute",
				bottom: 0,
				left: 0,
				right: 0,
				paddingTop: 10,
				alignItems: "center",
				height: 84 + navigationBarHeight,
				paddingBottom: 4 + navigationBarHeight,
				overflow: "visible",
			}}
		>
			<Button
				title="LOG IN OR SIGN UP"
				position="absolute"
				top={-60}
				left={16}
				right={16}
				onPress={handleLoginOrSignUp}
			/>
			<BlurView
				intensity={1}
				tint="dark"
				experimentalBlurMethod="dimezisBlurView"
				style={{
					position: "absolute",
					bottom: 0,
					left: 0,
					right: 0,
					height: 44 + navigationBarHeight,
				}}
			/>
			<View
				position="relative"
				flex={1}
				mb={16}
				borderRadius={99}
				pt={2}
				paddingHorizontal={2}
				pb={3}
			>
				<Image
					src={TabBg}
					objectFit="fill"
					position="absolute"
					top={0}
					left={0}
					bottom={1}
					right={0}
					width={"fill"}
					height={"fill"}
					borderRadius={99}
					opacity={0.8}
				/>
				<BlurView
					intensity={10}
					blurReductionFactor={20}
					tint="light"
					experimentalBlurMethod="dimezisBlurView"
					style={{
						flex: 1,
						flexDirection: "row",
						alignItems: "center",
						justifyContent: "center",
						borderRadius: 99,
						paddingHorizontal: 16,
						overflow: "hidden",
						gap: 10,
					}}
				>
					{/** biome-ignore lint/suspicious/noExplicitAny: route type from React Navigation */}
					{props.state.routes.map((route: any, index: number) => {
						const { options } = props.descriptors[route.key];

						const isFocused = props.state.index === index;

						const onPress = () => {
							const event = props.navigation.emit({
								type: "tabPress",
								target: route.key,
								canPreventDefault: true,
							});

							if (!isFocused && !event.defaultPrevented) {
								props.navigation.navigate(route.name);
							}
						};
						return (
							<TouchableOpacity
								key={route.name}
								onPress={onPress}
								activeOpacity={0.4}
							>
								<View
									flex={1}
									alignItems="center"
									justifyContent="center"
									width={40}
								>
									{options.tabBarIcon?.({
										focused: isFocused,
										color: isFocused ? "#BBFBBD" : "white",
										size: 24,
									})}
								</View>
							</TouchableOpacity>
						);
					})}
				</BlurView>
			</View>
		</LinearGradient>
	);
};
