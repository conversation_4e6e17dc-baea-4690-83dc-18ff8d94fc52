import { useState } from "react";
import { Ionicons } from "@expo/vector-icons";
import { FlatList, Keyboard } from "react-native";
import { H3, Text, YStack, Separator } from "tamagui";
import { Button, Input, ListItem } from "@/components";
import { DUMMY_CRYPTO_DATA } from "@/constants/dummyCryproData";

export default function ChooseCoinsScreen() {
	const [cryptoSearchQuery, setCryptoSearchQuery] = useState("");
	const [selectedCoins, setSelectedCoins] = useState<string[]>([]);
	const [isSearchFocused, setIsSearchFocused] = useState(false);

	const filteredCryptoData = DUMMY_CRYPTO_DATA.filter(
		(coin) =>
			coin.name.toLowerCase().includes(cryptoSearchQuery.toLowerCase()) ||
			coin.symbol.toLowerCase().includes(cryptoSearchQuery.toLowerCase()),
	);

	const showSearchResults = isSearchFocused && cryptoSearchQuery.length > 0;
	const coinsToShow =
		cryptoSearchQuery.length > 0 ? filteredCryptoData : DUMMY_CRYPTO_DATA;

	const handleCryptoSearch = (query: string) => {
		setCryptoSearchQuery(query);
	};

	const clearCryptoSearch = () => {
		setCryptoSearchQuery("");
	};

	const handleSearchFocus = () => {
		setIsSearchFocused(true);
	};

	const handleSearchBlur = () => {
		setIsSearchFocused(false);
		Keyboard.dismiss();
	};

	const toggleCoinSelection = (coinId: string) => {
		handleSearchBlur();

		setSelectedCoins((prev) => {
			if (prev.includes(coinId)) {
				return prev.filter((id) => id !== coinId);
			} else if (prev.length < 10) {
				return [...prev, coinId];
			}
			return prev;
		});
	};

	return (
		<YStack flex={1} gap="$6" mt={24} onPress={handleSearchBlur}>
			<YStack gap="$3">
				<H3 color={"$white1"} fontSize={36} lineHeight={36 * 1.3}>
					Choose your preferred coins
				</H3>

				<Text color={"$gray6"} fontSize={16} lineHeight={24}>
					(Select up to 10)
				</Text>
			</YStack>

			<YStack gap="$2">
				<Input
					placeholder="Search for any cryptocurrency..."
					value={cryptoSearchQuery}
					width={"100%"}
					paddingLeft={40}
					leftIcon={<Ionicons name="search" size={20} color="$gray6" />}
					rightIcon={
						cryptoSearchQuery ? (
							<Ionicons name="close-circle" size={20} color="$gray6" />
						) : null
					}
					onFocus={handleSearchFocus}
					onChangeText={handleCryptoSearch}
					rightIconPressable
					onRightIconPress={clearCryptoSearch}
				/>

				{showSearchResults ? (
					<YStack backgroundColor={"$gray1"} px={16}>
						<FlatList
							data={filteredCryptoData.slice(0, 3)}
							keyExtractor={(item) => item.id}
							renderItem={({ item }) => (
								<ListItem
									name={item.name}
									symbol={item.symbol}
									imageUrl={item.imageUrl}
									onPress={() => {
										toggleCoinSelection(item.id);
									}}
									hideCheckbox
								/>
							)}
							showsVerticalScrollIndicator={false}
						/>
					</YStack>
				) : null}
			</YStack>

			<FlatList
				data={coinsToShow}
				keyExtractor={(item) => item.id}
				renderItem={({ item }) => (
					<ListItem
						name={item.name}
						symbol={item.symbol}
						imageUrl={item.imageUrl}
						checked={selectedCoins.includes(item.id)}
						onPress={() => toggleCoinSelection(item.id)}
					/>
				)}
				showsVerticalScrollIndicator={false}
				ItemSeparatorComponent={() => (
					<Separator borderColor={"$border-gray"} />
				)}
			/>

			<YStack
				position="absolute"
				bottom={0}
				left={0}
				right={0}
				height={70}
				justifyContent="center"
				backgroundColor={"transparent"}
			>
				<Button title="NEXT QUESTION" onPress={() => {}} />
			</YStack>
		</YStack>
	);
}
