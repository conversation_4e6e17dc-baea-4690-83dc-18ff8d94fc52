interface CryptoCoin {
	id: string;
	name: string;
	symbol: string;
	imageUrl: string;
}

export const DUMMY_CRYPTO_DATA: CryptoCoin[] = [
	{
		id: "bitcoin",
		name: "Bitcoin",
		symbol: "BTC",
		imageUrl: "https://picsum.photos/100/100?random=1",
	},
	{
		id: "ethereum",
		name: "Ethereum",
		symbol: "ETH",
		imageUrl: "https://picsum.photos/100/100?random=2",
	},
	{
		id: "cardano",
		name: "Cardano",
		symbol: "ADA",
		imageUrl: "https://picsum.photos/100/100?random=3",
	},
	{
		id: "solana",
		name: "<PERSON><PERSON>",
		symbol: "SOL",
		imageUrl: "https://picsum.photos/100/100?random=4",
	},
	{
		id: "polkadot",
		name: "<PERSON><PERSON><PERSON>",
		symbol: "DOT",
		imageUrl: "https://picsum.photos/100/100?random=5",
	},
	{
		id: "chainlink",
		name: "Chainlink",
		symbol: "<PERSON>IN<PERSON>",
		imageUrl: "https://picsum.photos/100/100?random=6",
	},
	{
		id: "polygon",
		name: "<PERSON>ygon",
		symbol: "MATI<PERSON>",
		imageUrl: "https://picsum.photos/100/100?random=7",
	},
	{
		id: "avalanche",
		name: "Avalanche",
		symbol: "AVAX",
		imageUrl: "https://picsum.photos/100/100?random=8",
	},
	{
		id: "uniswap",
		name: "Uniswap",
		symbol: "UNI",
		imageUrl: "https://picsum.photos/100/100?random=2",
	},
	{
		id: "litecoin",
		name: "Litecoin",
		symbol: "LTC",
		imageUrl: "https://picsum.photos/100/100?random=9",
	},
];
