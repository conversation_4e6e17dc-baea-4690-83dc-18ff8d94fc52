import type { ApiResponse } from "./api";

interface Media {
	url: string;
	caption: string | null;
}
export interface PostData {
	id: string;
	user_screen_name: string;
	user_handle: string;
	user_profile_pic_url: string;
	channel_url: string;
	channel_name: string;
	source: "reddit" | "telegram" | "twitter";
	source_post_url: string;
	source_post_id: string;
	reply_count: number;
	repost_count: number;
	reaction_count: {
		heart: number;
		votes: number;
		likes: number;
	};
	view_count: number;
	sentiment_tags: string[];
	media: Media[];
	post_text: string;
	created_at: number;
}

export type PostResponse = ApiResponse<PostData[]>;
