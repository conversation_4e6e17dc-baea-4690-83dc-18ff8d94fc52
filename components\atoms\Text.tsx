import type React from "react";
import {
	Text as TamaguiText,
	type TextProps as TamaguiTextProps,
} from "tamagui";

/**
 * This Text component ensures consistent typography across app
 * by automatically selecting the appropriate Gabarito font variant
 * based on the desired weight.
 */
export interface TextProps extends Omit<TamaguiTextProps, "fontWeight"> {
	children?: React.ReactNode;
	fontWeight?: TamaguiTextProps["fontWeight"];
}

/**
 * Maps fontWeight values to corresponding Gabarito font family names
 */
const getFontFamily = (fontWeight?: TamaguiTextProps["fontWeight"]): string => {
	const weight =
		typeof fontWeight === "string"
			? parseStringFontWeight(fontWeight)
			: fontWeight || 400;

	switch (weight) {
		case 500:
			return "Gabarito_500Medium";
		case 600:
			return "Gabarito_600SemiBold";
		case 700:
			return "Gabarito_700Bold";
		case 800:
			return "Gabarito_800ExtraBold";
		case 900:
			return "Gabarito_900Black";
		default:
			return "Gabarito_400Regular";
	}
};

/**
 * Converts string fontWeight values to numeric equivalents
 */
const parseStringFontWeight = (fontWeight: string): number => {
	switch (fontWeight.toLowerCase()) {
		case "normal":
		case "regular":
			return 400;
		case "medium":
			return 500;
		case "semibold":
		case "semi-bold":
			return 600;
		case "bold":
			return 700;
		case "extrabold":
		case "extra-bold":
			return 800;
		case "black":
		case "heavy":
			return 900;
		default: {
			const parsed = parseInt(fontWeight, 10);
			return Number.isNaN(parsed) ? 400 : parsed;
		}
	}
};

export const Text = ({
	fontWeight,
	fontFamily,
	children,
	...props
}: TextProps) => {
	// Use provided fontFamily if specified, otherwise map from fontWeight
	const resolvedFontFamily = fontFamily || getFontFamily(fontWeight);

	return (
		<TamaguiText fontFamily={resolvedFontFamily} color="white" {...props}>
			{children}
		</TamaguiText>
	);
};
