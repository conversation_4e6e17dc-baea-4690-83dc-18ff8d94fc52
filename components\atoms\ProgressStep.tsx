import { XStack } from "tamagui";

// types
import type { XStackProps } from "tamagui";
import type React from "react";
import type { ViewStyle } from "react-native";

export interface ProgressStepProps
	extends Omit<XStackProps, "backgroundColor"> {
	isActive: boolean;
	height?: number;
	activeColors?: string;
	inactiveColors?: string;
	borderRadius?: number;
	style?: ViewStyle;
}

export const ProgressStep: React.FC<ProgressStepProps> = ({
	isActive,
	height = 4,
	activeColors = "gray7",
	inactiveColors = "$gray2",
	borderRadius = 2,
	style,
	...props
}) => {
	const color = isActive ? activeColors : inactiveColors;

	return <XStack height={height} backgroundColor={color} flex={1} {...props} />;
};

export interface ProgressStepperProps extends Omit<XStackProps, "gap"> {
	totalSteps: number;
	currentStep: number;
	height?: number;
	gap?: number;
	activeColors?: string;
	inactiveColors?: string;
	borderRadius?: number;
}

export const ProgressStepper: React.FC<ProgressStepperProps> = ({
	totalSteps,
	currentStep,
	height = 4,
	gap = 4,
	activeColors = "gray7",
	inactiveColors = "$gray2",
	borderRadius = 2,
	...props
}) => {
	const steps = Array.from({ length: totalSteps }, (_, index) => index);

	return (
		<XStack gap={gap} flex={1} {...props}>
			{steps.map((stepIndex) => (
				<ProgressStep
					key={stepIndex}
					isActive={stepIndex <= currentStep - 1}
					height={height}
					activeColors={activeColors}
					inactiveColors={inactiveColors}
					borderRadius={borderRadius}
				/>
			))}
		</XStack>
	);
};
